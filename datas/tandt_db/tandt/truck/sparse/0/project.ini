log_to_stderr=false
log_level=2
random_seed=0
database_path=
image_path=
[ImageReader]
single_camera=false
single_camera_per_folder=false
single_camera_per_image=false
existing_camera_id=-1
default_focal_length_factor=1.2
mask_path=
camera_model=SIMPLE_RADIAL
camera_params=
camera_mask_path=
[SiftExtraction]
use_gpu=true
estimate_affine_shape=false
upright=false
domain_size_pooling=false
num_threads=-1
max_image_size=3200
max_num_features=8192
first_octave=-1
num_octaves=4
octave_resolution=3
max_num_orientations=2
dsp_num_scales=10
peak_threshold=0.0066666666666666671
edge_threshold=10
dsp_min_scale=0.16666666666666666
dsp_max_scale=3
gpu_index=-1
[SiftMatching]
use_gpu=true
cross_check=true
multiple_models=false
guided_matching=false
planar_scene=false
compute_relative_pose=false
num_threads=-1
max_num_matches=32768
max_num_trials=10000
min_num_inliers=15
max_ratio=0.80000000000000004
max_distance=0.69999999999999996
max_error=4
confidence=0.999
min_inlier_ratio=0.25
gpu_index=-1
[SequentialMatching]
quadratic_overlap=true
loop_detection=false
overlap=10
loop_detection_period=10
loop_detection_num_images=50
loop_detection_num_nearest_neighbors=1
loop_detection_num_checks=256
loop_detection_num_images_after_verification=0
loop_detection_max_num_features=-1
vocab_tree_path=
[SpatialMatching]
is_gps=true
ignore_z=true
max_num_neighbors=50
max_distance=100
[BundleAdjustment]
refine_focal_length=true
refine_principal_point=false
refine_extra_params=true
refine_extrinsics=true
max_num_iterations=100
max_linear_solver_iterations=200
function_tolerance=0
gradient_tolerance=0
parameter_tolerance=0
[Mapper]
ignore_watermarks=false
multiple_models=true
extract_colors=true
ba_refine_focal_length=true
ba_refine_principal_point=false
ba_refine_extra_params=true
ba_global_use_pba=false
fix_existing_images=false
tri_ignore_two_view_tracks=true
min_num_matches=15
max_num_models=50
max_model_overlap=20
min_model_size=10
init_image_id1=-1
init_image_id2=-1
init_num_trials=200
num_threads=-1
ba_min_num_residuals_for_multi_threading=50000
ba_local_num_images=6
ba_local_max_num_iterations=25
ba_global_pba_gpu_index=-1
ba_global_images_freq=500
ba_global_points_freq=250000
ba_global_max_num_iterations=50
ba_global_max_refinements=5
ba_local_max_refinements=2
snapshot_images_freq=0
init_min_num_inliers=100
init_max_reg_trials=2
abs_pose_min_num_inliers=30
max_reg_trials=3
tri_max_transitivity=1
tri_complete_max_transitivity=5
tri_re_max_trials=1
min_focal_length_ratio=0.10000000000000001
max_focal_length_ratio=10
max_extra_param=1
ba_local_function_tolerance=0
ba_global_images_ratio=1.1000000000000001
ba_global_points_ratio=1.1000000000000001
ba_global_function_tolerance=0
ba_global_max_refinement_change=0.00050000000000000001
ba_local_max_refinement_change=0.001
init_max_error=4
init_max_forward_motion=0.94999999999999996
init_min_tri_angle=16
abs_pose_max_error=12
abs_pose_min_inlier_ratio=0.25
filter_max_reproj_error=4
filter_min_tri_angle=1.5
local_ba_min_tri_angle=6
tri_create_max_angle_error=2
tri_continue_max_angle_error=2
tri_merge_max_reproj_error=4
tri_complete_max_reproj_error=4
tri_re_max_angle_error=5
tri_re_min_ratio=0.20000000000000001
tri_min_angle=1.5
snapshot_path=
[PatchMatchStereo]
geom_consistency=true
filter=true
allow_missing_files=false
write_consistency_graph=false
max_image_size=2000
window_radius=5
window_step=1
num_samples=15
num_iterations=5
filter_min_num_consistent=2
depth_min=-1
depth_max=-1
sigma_spatial=-1
sigma_color=0.20000000298023224
ncc_sigma=0.60000002384185791
min_triangulation_angle=1
incident_angle_sigma=0.89999997615814209
geom_consistency_regularizer=0.30000001192092896
geom_consistency_max_cost=3
filter_min_ncc=0.10000000149011612
filter_min_triangulation_angle=3
filter_geom_consistency_max_cost=1
cache_size=32
gpu_index=-1
[StereoFusion]
use_cache=false
num_threads=-1
max_image_size=-1
min_num_pixels=5
max_num_pixels=10000
max_traversal_depth=100
check_num_images=50
max_reproj_error=2
max_depth_error=0.0099999997764825821
max_normal_error=10
cache_size=32
mask_path=
[Render]
adapt_refresh_rate=true
image_connections=false
min_track_len=3
refresh_rate=1
projection_type=0
max_error=2
[ExhaustiveMatching]
block_size=50
[VocabTreeMatching]
num_images=100
num_nearest_neighbors=5
num_checks=256
num_images_after_verification=0
max_num_features=-1
vocab_tree_path=
match_list_path=
[TransitiveMatching]
batch_size=1000
num_iterations=3
[ImagePairsMatching]
block_size=1225
[PoissonMeshing]
depth=13
num_threads=-1
point_weight=1
color=32
trim=10
[DelaunayMeshing]
num_threads=-1
max_proj_dist=20
max_depth_dist=0.050000000000000003
visibility_sigma=3
distance_sigma_factor=1
quality_regularization=1
max_side_length_factor=25
max_side_length_percentile=95
