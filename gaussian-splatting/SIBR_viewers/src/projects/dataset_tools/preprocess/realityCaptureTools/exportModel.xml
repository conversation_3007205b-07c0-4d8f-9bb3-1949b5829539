<Configuration>
  <entry key="ModelExportFormatVersion" value="0"/>
  <entry key="MvsMeshExportCamerasAsModelPart" value="false"/>
  <entry key="MvsMeshExportTexturingAllowed" value="-1"/>
  <entry key="MvsExportIsModelCoordinates" value="0"/>
  <entry key="MvsExportIsGeoreferenced" value="0x0"/>
  <entry key="MvsExportScaleZ" value="1.0"/>
  <entry key="MvsMeshExportTileType" value="0"/>
  <entry key="MvsMeshExportNormals" value="true"/>
  <entry key="MvsExportScaleY" value="1.0"/>
  <entry key="MvsMeshExportTexAlpha" value="false"/>
  <entry key="MvsExportScaleX" value="1.0"/>
  <entry key="MvsMeshExportTexImgFormat_Color8_0" value="png"/>
  <entry key="MvsExportcoordinatesystemtype" value="0"/>
  <entry key="MvsMeshExportTexPixFormat_Color8_0" value="32bppBGRA"/>
  <entry key="MvsMeshExportNormalsAllowed" value="-1"/>
  <entry key="MvsMeshExportNumberFormatAllowed" value="-1"/>
  <entry key="MvsExportMoveZ" value="0.0"/>
  <entry key="MvsExportMoveX" value="0.0"/>
  <entry key="MvsExportMoveY" value="0.0"/>
  <entry key="MvsExportNormalRange" value="ZeroToOne"/>
  <entry key="MvsMeshExportInfoFile" value="true"/>
  <entry key="MvsMeshExportByParts" value="0"/>
  <entry key="MvsMeshExportClassificationAllowed" value="0"/>
  <entry key="MvsMeshExportNumberFormat" value="5"/>
  <entry key="MvsExportRotationY" value="0.0"/>
  <entry key="MvsExportNormalFlipZ" value="false"/>
  <entry key="MvsExportRotationX" value="0.0"/>
  <entry key="MvsExportNormalFlipY" value="false"/>
  <entry key="MvsMeshExportCamerasAllowed" value="0"/>
  <entry key="MvsMeshExportColors" value="true"/>
  <entry key="MvsExportNormalSpace" value="Mikktspace"/>
  <entry key="MvsExportNormalFlipX" value="false"/>
  <entry key="MvsExportTransformationPreset" value="[[Default]]"/>
  <entry key="MvsExportRotationZ" value="0.0"/>
  <entry key="MvsMeshExportFileTypeSelectionDisplay" value="0"/>
  <entry key="MvsMeshExportTexOneFile" value="0"/>
  <entry key="MvsMeshExportTexturing" value="-1"/>
  <entry key="MvsMeshExportEmbeddTxrsAllowed" value="0"/>
</Configuration>
