<!DOCTYPE FilterScript>
<FilterScript>
 <filter name="Remove Duplicate Vertices"/>
 <filter name="Remove Unreferenced Vertices"/>
 <filter name="Simplification: Quadric Edge Collapse Decimation">
  <Param name="TargetFaceNum" type="RichInt" tooltip="The desired final number of faces." value="300000" description="Target number of faces"/>
  <Param name="TargetPerc" type="RichFloat" tooltip="If non zero, this parameter specifies the desired final size of the mesh as a percentage of the initial size." value="0" description="Percentage reduction (0..1)"/>
  <Param name="QualityThr" type="RichFloat" tooltip="Quality threshold for penalizing bad shaped faces.&lt;br>The value is in the range [0..1]&#xa; 0 accept any kind of face (no penalties),&#xa; 0.5  penalize faces with quality &lt; 0.5, proportionally to their shape&#xa;" value="0.3" description="Quality threshold"/>
  <Param name="PreserveBoundary" type="RichBool" tooltip="The simplification process tries to do not affect mesh boundaries during simplification" value="false" description="Preserve Boundary of the mesh"/>
  <Param name="BoundaryWeight" type="RichFloat" tooltip="The importance of the boundary during simplification. Default (1.0) means that the boundary has the same importance of the rest. Values greater than 1.0 raise boundary importance and has the effect of removing less vertices on the border. Admitted range of values (0,+inf). " value="1" description="Boundary Preserving Weight"/>
  <Param name="PreserveNormal" type="RichBool" tooltip="Try to avoid face flipping effects and try to preserve the original orientation of the surface" value="false" description="Preserve Normal"/>
  <Param name="PreserveTopology" type="RichBool" tooltip="Avoid all the collapses that should cause a topology change in the mesh (like closing holes, squeezing handles, etc). If checked the genus of the mesh should stay unchanged." value="false" description="Preserve Topology"/>
  <Param name="OptimalPlacement" type="RichBool" tooltip="Each collapsed vertex is placed in the position minimizing the quadric error.&#xa; It can fail (creating bad spikes) in case of very flat areas. &#xa;If disabled edges are collapsed onto one of the two original vertices and the final mesh is composed by a subset of the original vertices. " value="true" description="Optimal position of simplified vertices"/>
  <Param name="PlanarQuadric" type="RichBool" tooltip="Add additional simplification constraints that improves the quality of the simplification of the planar portion of the mesh, as a side effect, more triangles will be preserved in flat areas (allowing better shaped triangles)." value="false" description="Planar Simplification"/>
  <Param name="PlanarWeight" type="RichFloat" tooltip="How much we should try to preserve the triangles in the planar regions. If you lower this value planar areas will be simplified more." value="0.001" description="Planar Simp. Weight"/>
  <Param name="QualityWeight" type="RichBool" tooltip="Use the Per-Vertex quality as a weighting factor for the simplification. The weight is used as a error amplification value, so a vertex with a high quality value will not be simplified and a portion of the mesh with low quality values will be aggressively simplified." value="false" description="Weighted Simplification"/>
  <Param name="AutoClean" type="RichBool" tooltip="After the simplification an additional set of steps is performed to clean the mesh (unreferenced vertices, bad faces, etc)" value="true" description="Post-simplification cleaning"/>
  <Param name="Selected" type="RichBool" tooltip="The simplification is applied only to the selected set of faces.&#xa; Take care of the target number of faces!" value="false" description="Simplify only selected faces"/>
 </filter>
</FilterScript>
