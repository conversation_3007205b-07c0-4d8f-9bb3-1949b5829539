/*
 * Copyright (C) 2020, Inria
 * GRAPHDECO research group, https://team.inria.fr/graphdeco
 * All rights reserved.
 *
 * This software is free for non-commercial, research and evaluation use 
 * under the terms of the LICENSE.md file.
 *
 * For <NAME_EMAIL> and/or <PERSON><PERSON>@inria.fr
 */


#version 420

uniform vec2 position;
uniform vec2 scale;
uniform int count;

layout(location = 0) in vec3 in_vertex;
out vec2 uv_coord;

void main(void) {
	uv_coord = vec2(count+0.5, 1.0) * (in_vertex.xy * 0.5 + 0.5);
	gl_Position = vec4(scale * vec2(count, 1.0) * (in_vertex.xy - position) + position,0.0, 1.0);
}
