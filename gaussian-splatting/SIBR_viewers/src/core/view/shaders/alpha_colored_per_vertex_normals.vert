/*
 * Copyright (C) 2020, Inria
 * GRAPHDECO research group, https://team.inria.fr/graphdeco
 * All rights reserved.
 *
 * This software is free for non-commercial, research and evaluation use 
 * under the terms of the LICENSE.md file.
 *
 * For <NAME_EMAIL> and/or <PERSON><PERSON>@inria.fr
 */


#version 420

layout(location = 0) in vec3 in_vertex;   
layout(location = 3) in vec3 in_normal;  

out vec3 normals;

void main(void) {
    gl_Position = vec4(in_vertex, 1.0);
	normals = in_normal;
}
