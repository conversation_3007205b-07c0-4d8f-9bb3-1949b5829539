/*
 * Copyright (C) 2020, Inria
 * GRAPHDECO research group, https://team.inria.fr/graphdeco
 * All rights reserved.
 *
 * This software is free for non-commercial, research and evaluation use 
 * under the terms of the LICENSE.md file.
 *
 * For <NAME_EMAIL> and/or <PERSON><PERSON>@inria.fr
 */



#version 420

out VSOUT
{
  vec3 tc;
} out_Vert;

uniform mat4 in_View;
uniform vec2 in_Aspect;


const float fov = 70.0;
const float vecZ = in_Aspect.y / tan(radians(fov / 2.0));
//const float vecZ = 0.**********;

mat3 rotationMatrix(vec3 axis, float angle)
{
  axis = normalize(axis);
  float s = sin(angle);
  float c = cos(angle);
  float oc = 1.0 - c;
  return mat3(oc * axis.x * axis.x + c, oc * axis.x * axis.y - axis.z * s, oc * axis.z * axis.x + axis.y * s,
	      oc * axis.x * axis.y + axis.z * s, oc * axis.y * axis.y + c, oc * axis.y * axis.z - axis.x * s,
	      oc * axis.z * axis.x - axis.y * s, oc * axis.y * axis.z + axis.x * s, oc * axis.z * axis.z + c);
}

void main(void)
{

  vec2[4] vertices = vec2[4](vec2(-1.0, -1.0),
  			     vec2( 1.0, -1.0),
  			     vec2(-1.0,  1.0),
  			     vec2( 1.0,  1.0));


  vec3 vertex = vec3(vertices[gl_VertexID], -1.0);

  // out gl_Position
  gl_Position = vec4(vertex, 1.0);

  vertex.y = vertex.y * in_Aspect.y;
  vertex.z = -vecZ;

  out_Vert.tc = mat3(in_View) * vertex;
  //out_Vert.tc = rotationMatrix(vec3(1,0,0), -1.14) * out_Vert.tc;
  out_Vert.tc.z = -out_Vert.tc.z;
}
