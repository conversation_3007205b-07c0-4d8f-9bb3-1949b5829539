<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_compatibility</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a> &#124;
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_compatibility<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00017.html" title="GLM_GTX_compatibility ">glm/gtx/compatibility.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:gaddcd7aa2e30e61af5b38660613d3979e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaddcd7aa2e30e61af5b38660613d3979e"></a>
typedef bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gaddcd7aa2e30e61af5b38660613d3979e">bool1</a></td></tr>
<tr class="memdesc:gaddcd7aa2e30e61af5b38660613d3979e"><td class="mdescLeft">&#160;</td><td class="mdescRight">boolean type with 1 component. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gaddcd7aa2e30e61af5b38660613d3979e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7f895c936f0c29c8729afbbf22806090"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga7f895c936f0c29c8729afbbf22806090"></a>
typedef bool&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga7f895c936f0c29c8729afbbf22806090">bool1x1</a></td></tr>
<tr class="memdesc:ga7f895c936f0c29c8729afbbf22806090"><td class="mdescLeft">&#160;</td><td class="mdescRight">boolean matrix with 1 x 1 component. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga7f895c936f0c29c8729afbbf22806090"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa09ab65ec9c3c54305ff502e2b1fe6d9"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaa09ab65ec9c3c54305ff502e2b1fe6d9"></a>
typedef vec&lt; 2, bool, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gaa09ab65ec9c3c54305ff502e2b1fe6d9">bool2</a></td></tr>
<tr class="memdesc:gaa09ab65ec9c3c54305ff502e2b1fe6d9"><td class="mdescLeft">&#160;</td><td class="mdescRight">boolean type with 2 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gaa09ab65ec9c3c54305ff502e2b1fe6d9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadb3703955e513632f98ba12fe051ba3e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gadb3703955e513632f98ba12fe051ba3e"></a>
typedef mat&lt; 2, 2, bool, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gadb3703955e513632f98ba12fe051ba3e">bool2x2</a></td></tr>
<tr class="memdesc:gadb3703955e513632f98ba12fe051ba3e"><td class="mdescLeft">&#160;</td><td class="mdescRight">boolean matrix with 2 x 2 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gadb3703955e513632f98ba12fe051ba3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9ae6ee155d0f90cb1ae5b6c4546738a0"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga9ae6ee155d0f90cb1ae5b6c4546738a0"></a>
typedef mat&lt; 2, 3, bool, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga9ae6ee155d0f90cb1ae5b6c4546738a0">bool2x3</a></td></tr>
<tr class="memdesc:ga9ae6ee155d0f90cb1ae5b6c4546738a0"><td class="mdescLeft">&#160;</td><td class="mdescRight">boolean matrix with 2 x 3 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga9ae6ee155d0f90cb1ae5b6c4546738a0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4d7fa65be8e8e4ad6d920b45c44e471f"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga4d7fa65be8e8e4ad6d920b45c44e471f"></a>
typedef mat&lt; 2, 4, bool, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga4d7fa65be8e8e4ad6d920b45c44e471f">bool2x4</a></td></tr>
<tr class="memdesc:ga4d7fa65be8e8e4ad6d920b45c44e471f"><td class="mdescLeft">&#160;</td><td class="mdescRight">boolean matrix with 2 x 4 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga4d7fa65be8e8e4ad6d920b45c44e471f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga99629f818737f342204071ef8296b2ed"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga99629f818737f342204071ef8296b2ed"></a>
typedef vec&lt; 3, bool, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga99629f818737f342204071ef8296b2ed">bool3</a></td></tr>
<tr class="memdesc:ga99629f818737f342204071ef8296b2ed"><td class="mdescLeft">&#160;</td><td class="mdescRight">boolean type with 3 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga99629f818737f342204071ef8296b2ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac7d7311f7e0fa8b6163d96dab033a755"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gac7d7311f7e0fa8b6163d96dab033a755"></a>
typedef mat&lt; 3, 2, bool, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gac7d7311f7e0fa8b6163d96dab033a755">bool3x2</a></td></tr>
<tr class="memdesc:gac7d7311f7e0fa8b6163d96dab033a755"><td class="mdescLeft">&#160;</td><td class="mdescRight">boolean matrix with 3 x 2 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gac7d7311f7e0fa8b6163d96dab033a755"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6c97b99aac3e302053ffb58aace9033c"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga6c97b99aac3e302053ffb58aace9033c"></a>
typedef mat&lt; 3, 3, bool, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga6c97b99aac3e302053ffb58aace9033c">bool3x3</a></td></tr>
<tr class="memdesc:ga6c97b99aac3e302053ffb58aace9033c"><td class="mdescLeft">&#160;</td><td class="mdescRight">boolean matrix with 3 x 3 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga6c97b99aac3e302053ffb58aace9033c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae7d6b679463d37d6c527d478fb470fdf"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gae7d6b679463d37d6c527d478fb470fdf"></a>
typedef mat&lt; 3, 4, bool, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gae7d6b679463d37d6c527d478fb470fdf">bool3x4</a></td></tr>
<tr class="memdesc:gae7d6b679463d37d6c527d478fb470fdf"><td class="mdescLeft">&#160;</td><td class="mdescRight">boolean matrix with 3 x 4 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gae7d6b679463d37d6c527d478fb470fdf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga13c3200b82708f73faac6d7f09ec91a3"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga13c3200b82708f73faac6d7f09ec91a3"></a>
typedef vec&lt; 4, bool, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga13c3200b82708f73faac6d7f09ec91a3">bool4</a></td></tr>
<tr class="memdesc:ga13c3200b82708f73faac6d7f09ec91a3"><td class="mdescLeft">&#160;</td><td class="mdescRight">boolean type with 4 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga13c3200b82708f73faac6d7f09ec91a3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9ed830f52408b2f83c085063a3eaf1d0"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga9ed830f52408b2f83c085063a3eaf1d0"></a>
typedef mat&lt; 4, 2, bool, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga9ed830f52408b2f83c085063a3eaf1d0">bool4x2</a></td></tr>
<tr class="memdesc:ga9ed830f52408b2f83c085063a3eaf1d0"><td class="mdescLeft">&#160;</td><td class="mdescRight">boolean matrix with 4 x 2 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga9ed830f52408b2f83c085063a3eaf1d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad0f5dc7f22c2065b1b06d57f1c0658fe"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gad0f5dc7f22c2065b1b06d57f1c0658fe"></a>
typedef mat&lt; 4, 3, bool, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gad0f5dc7f22c2065b1b06d57f1c0658fe">bool4x3</a></td></tr>
<tr class="memdesc:gad0f5dc7f22c2065b1b06d57f1c0658fe"><td class="mdescLeft">&#160;</td><td class="mdescRight">boolean matrix with 4 x 3 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gad0f5dc7f22c2065b1b06d57f1c0658fe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7d2a7d13986602ae2896bfaa394235d4"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga7d2a7d13986602ae2896bfaa394235d4"></a>
typedef mat&lt; 4, 4, bool, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga7d2a7d13986602ae2896bfaa394235d4">bool4x4</a></td></tr>
<tr class="memdesc:ga7d2a7d13986602ae2896bfaa394235d4"><td class="mdescLeft">&#160;</td><td class="mdescRight">boolean matrix with 4 x 4 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga7d2a7d13986602ae2896bfaa394235d4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga20b861a9b6e2a300323671c57a02525b"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga20b861a9b6e2a300323671c57a02525b"></a>
typedef double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga20b861a9b6e2a300323671c57a02525b">double1</a></td></tr>
<tr class="memdesc:ga20b861a9b6e2a300323671c57a02525b"><td class="mdescLeft">&#160;</td><td class="mdescRight">double-qualifier floating-point vector with 1 component. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga20b861a9b6e2a300323671c57a02525b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga45f16a4dd0db1f199afaed9fd12fe9a8"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga45f16a4dd0db1f199afaed9fd12fe9a8"></a>
typedef double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga45f16a4dd0db1f199afaed9fd12fe9a8">double1x1</a></td></tr>
<tr class="memdesc:ga45f16a4dd0db1f199afaed9fd12fe9a8"><td class="mdescLeft">&#160;</td><td class="mdescRight">double-qualifier floating-point matrix with 1 component. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga45f16a4dd0db1f199afaed9fd12fe9a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga31b729b04facccda73f07ed26958b3c2"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga31b729b04facccda73f07ed26958b3c2"></a>
typedef vec&lt; 2, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga31b729b04facccda73f07ed26958b3c2">double2</a></td></tr>
<tr class="memdesc:ga31b729b04facccda73f07ed26958b3c2"><td class="mdescLeft">&#160;</td><td class="mdescRight">double-qualifier floating-point vector with 2 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga31b729b04facccda73f07ed26958b3c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae57d0201096834d25f2b91b319e7cdbd"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gae57d0201096834d25f2b91b319e7cdbd"></a>
typedef mat&lt; 2, 2, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gae57d0201096834d25f2b91b319e7cdbd">double2x2</a></td></tr>
<tr class="memdesc:gae57d0201096834d25f2b91b319e7cdbd"><td class="mdescLeft">&#160;</td><td class="mdescRight">double-qualifier floating-point matrix with 2 x 2 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gae57d0201096834d25f2b91b319e7cdbd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3655bc324008553ca61f39952d0b2d08"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga3655bc324008553ca61f39952d0b2d08"></a>
typedef mat&lt; 2, 3, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga3655bc324008553ca61f39952d0b2d08">double2x3</a></td></tr>
<tr class="memdesc:ga3655bc324008553ca61f39952d0b2d08"><td class="mdescLeft">&#160;</td><td class="mdescRight">double-qualifier floating-point matrix with 2 x 3 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga3655bc324008553ca61f39952d0b2d08"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacd33061fc64a7b2dcfd7322c49d9557a"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gacd33061fc64a7b2dcfd7322c49d9557a"></a>
typedef mat&lt; 2, 4, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gacd33061fc64a7b2dcfd7322c49d9557a">double2x4</a></td></tr>
<tr class="memdesc:gacd33061fc64a7b2dcfd7322c49d9557a"><td class="mdescLeft">&#160;</td><td class="mdescRight">double-qualifier floating-point matrix with 2 x 4 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gacd33061fc64a7b2dcfd7322c49d9557a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3d8b9028a1053a44a98902cd1c389472"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga3d8b9028a1053a44a98902cd1c389472"></a>
typedef vec&lt; 3, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga3d8b9028a1053a44a98902cd1c389472">double3</a></td></tr>
<tr class="memdesc:ga3d8b9028a1053a44a98902cd1c389472"><td class="mdescLeft">&#160;</td><td class="mdescRight">double-qualifier floating-point vector with 3 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga3d8b9028a1053a44a98902cd1c389472"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5ec08fc39c9d783dfcc488be240fe975"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga5ec08fc39c9d783dfcc488be240fe975"></a>
typedef mat&lt; 3, 2, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga5ec08fc39c9d783dfcc488be240fe975">double3x2</a></td></tr>
<tr class="memdesc:ga5ec08fc39c9d783dfcc488be240fe975"><td class="mdescLeft">&#160;</td><td class="mdescRight">double-qualifier floating-point matrix with 3 x 2 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga5ec08fc39c9d783dfcc488be240fe975"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4bad5bb20c6ddaecfe4006c93841d180"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga4bad5bb20c6ddaecfe4006c93841d180"></a>
typedef mat&lt; 3, 3, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga4bad5bb20c6ddaecfe4006c93841d180">double3x3</a></td></tr>
<tr class="memdesc:ga4bad5bb20c6ddaecfe4006c93841d180"><td class="mdescLeft">&#160;</td><td class="mdescRight">double-qualifier floating-point matrix with 3 x 3 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga4bad5bb20c6ddaecfe4006c93841d180"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2ef022e453d663d70aec414b2a80f756"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga2ef022e453d663d70aec414b2a80f756"></a>
typedef mat&lt; 3, 4, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga2ef022e453d663d70aec414b2a80f756">double3x4</a></td></tr>
<tr class="memdesc:ga2ef022e453d663d70aec414b2a80f756"><td class="mdescLeft">&#160;</td><td class="mdescRight">double-qualifier floating-point matrix with 3 x 4 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga2ef022e453d663d70aec414b2a80f756"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf92f58af24f35617518aeb3d4f63fda6"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaf92f58af24f35617518aeb3d4f63fda6"></a>
typedef vec&lt; 4, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gaf92f58af24f35617518aeb3d4f63fda6">double4</a></td></tr>
<tr class="memdesc:gaf92f58af24f35617518aeb3d4f63fda6"><td class="mdescLeft">&#160;</td><td class="mdescRight">double-qualifier floating-point vector with 4 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gaf92f58af24f35617518aeb3d4f63fda6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabca29ccceea53669618b751aae0ba83d"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gabca29ccceea53669618b751aae0ba83d"></a>
typedef mat&lt; 4, 2, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gabca29ccceea53669618b751aae0ba83d">double4x2</a></td></tr>
<tr class="memdesc:gabca29ccceea53669618b751aae0ba83d"><td class="mdescLeft">&#160;</td><td class="mdescRight">double-qualifier floating-point matrix with 4 x 2 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gabca29ccceea53669618b751aae0ba83d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafad66a02ccd360c86d6ab9ff9cfbc19c"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gafad66a02ccd360c86d6ab9ff9cfbc19c"></a>
typedef mat&lt; 4, 3, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gafad66a02ccd360c86d6ab9ff9cfbc19c">double4x3</a></td></tr>
<tr class="memdesc:gafad66a02ccd360c86d6ab9ff9cfbc19c"><td class="mdescLeft">&#160;</td><td class="mdescRight">double-qualifier floating-point matrix with 4 x 3 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gafad66a02ccd360c86d6ab9ff9cfbc19c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaab541bed2e788e4537852a2492860806"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaab541bed2e788e4537852a2492860806"></a>
typedef mat&lt; 4, 4, double, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gaab541bed2e788e4537852a2492860806">double4x4</a></td></tr>
<tr class="memdesc:gaab541bed2e788e4537852a2492860806"><td class="mdescLeft">&#160;</td><td class="mdescRight">double-qualifier floating-point matrix with 4 x 4 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gaab541bed2e788e4537852a2492860806"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf5208d01f6c6fbcb7bb55d610b9c0ead"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaf5208d01f6c6fbcb7bb55d610b9c0ead"></a>
typedef float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gaf5208d01f6c6fbcb7bb55d610b9c0ead">float1</a></td></tr>
<tr class="memdesc:gaf5208d01f6c6fbcb7bb55d610b9c0ead"><td class="mdescLeft">&#160;</td><td class="mdescRight">single-qualifier floating-point vector with 1 component. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gaf5208d01f6c6fbcb7bb55d610b9c0ead"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga73720b8dc4620835b17f74d428f98c0c"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga73720b8dc4620835b17f74d428f98c0c"></a>
typedef float&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga73720b8dc4620835b17f74d428f98c0c">float1x1</a></td></tr>
<tr class="memdesc:ga73720b8dc4620835b17f74d428f98c0c"><td class="mdescLeft">&#160;</td><td class="mdescRight">single-qualifier floating-point matrix with 1 component. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga73720b8dc4620835b17f74d428f98c0c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga02d3c013982c183906c61d74aa3166ce"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga02d3c013982c183906c61d74aa3166ce"></a>
typedef vec&lt; 2, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga02d3c013982c183906c61d74aa3166ce">float2</a></td></tr>
<tr class="memdesc:ga02d3c013982c183906c61d74aa3166ce"><td class="mdescLeft">&#160;</td><td class="mdescRight">single-qualifier floating-point vector with 2 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga02d3c013982c183906c61d74aa3166ce"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga33d43ecbb60a85a1366ff83f8a0ec85f"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga33d43ecbb60a85a1366ff83f8a0ec85f"></a>
typedef mat&lt; 2, 2, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga33d43ecbb60a85a1366ff83f8a0ec85f">float2x2</a></td></tr>
<tr class="memdesc:ga33d43ecbb60a85a1366ff83f8a0ec85f"><td class="mdescLeft">&#160;</td><td class="mdescRight">single-qualifier floating-point matrix with 2 x 2 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga33d43ecbb60a85a1366ff83f8a0ec85f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga939b0cff15cee3030f75c1b2e36f89fe"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga939b0cff15cee3030f75c1b2e36f89fe"></a>
typedef mat&lt; 2, 3, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga939b0cff15cee3030f75c1b2e36f89fe">float2x3</a></td></tr>
<tr class="memdesc:ga939b0cff15cee3030f75c1b2e36f89fe"><td class="mdescLeft">&#160;</td><td class="mdescRight">single-qualifier floating-point matrix with 2 x 3 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga939b0cff15cee3030f75c1b2e36f89fe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafec3cfd901ab334a92e0242b8f2269b4"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gafec3cfd901ab334a92e0242b8f2269b4"></a>
typedef mat&lt; 2, 4, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gafec3cfd901ab334a92e0242b8f2269b4">float2x4</a></td></tr>
<tr class="memdesc:gafec3cfd901ab334a92e0242b8f2269b4"><td class="mdescLeft">&#160;</td><td class="mdescRight">single-qualifier floating-point matrix with 2 x 4 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gafec3cfd901ab334a92e0242b8f2269b4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga821ff110fc8533a053cbfcc93e078cc0"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga821ff110fc8533a053cbfcc93e078cc0"></a>
typedef vec&lt; 3, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga821ff110fc8533a053cbfcc93e078cc0">float3</a></td></tr>
<tr class="memdesc:ga821ff110fc8533a053cbfcc93e078cc0"><td class="mdescLeft">&#160;</td><td class="mdescRight">single-qualifier floating-point vector with 3 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga821ff110fc8533a053cbfcc93e078cc0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa6c69f04ba95f3faedf95dae874de576"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaa6c69f04ba95f3faedf95dae874de576"></a>
typedef mat&lt; 3, 2, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gaa6c69f04ba95f3faedf95dae874de576">float3x2</a></td></tr>
<tr class="memdesc:gaa6c69f04ba95f3faedf95dae874de576"><td class="mdescLeft">&#160;</td><td class="mdescRight">single-qualifier floating-point matrix with 3 x 2 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gaa6c69f04ba95f3faedf95dae874de576"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6ceb5d38a58becdf420026e12a6562f3"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga6ceb5d38a58becdf420026e12a6562f3"></a>
typedef mat&lt; 3, 3, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga6ceb5d38a58becdf420026e12a6562f3">float3x3</a></td></tr>
<tr class="memdesc:ga6ceb5d38a58becdf420026e12a6562f3"><td class="mdescLeft">&#160;</td><td class="mdescRight">single-qualifier floating-point matrix with 3 x 3 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga6ceb5d38a58becdf420026e12a6562f3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4d2679c321b793ca3784fe0315bb5332"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga4d2679c321b793ca3784fe0315bb5332"></a>
typedef mat&lt; 3, 4, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga4d2679c321b793ca3784fe0315bb5332">float3x4</a></td></tr>
<tr class="memdesc:ga4d2679c321b793ca3784fe0315bb5332"><td class="mdescLeft">&#160;</td><td class="mdescRight">single-qualifier floating-point matrix with 3 x 4 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga4d2679c321b793ca3784fe0315bb5332"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae2da7345087db3815a25d8837a727ef1"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gae2da7345087db3815a25d8837a727ef1"></a>
typedef vec&lt; 4, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gae2da7345087db3815a25d8837a727ef1">float4</a></td></tr>
<tr class="memdesc:gae2da7345087db3815a25d8837a727ef1"><td class="mdescLeft">&#160;</td><td class="mdescRight">single-qualifier floating-point vector with 4 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gae2da7345087db3815a25d8837a727ef1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga308b9af0c221145bcfe9bfc129d9098e"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga308b9af0c221145bcfe9bfc129d9098e"></a>
typedef mat&lt; 4, 2, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga308b9af0c221145bcfe9bfc129d9098e">float4x2</a></td></tr>
<tr class="memdesc:ga308b9af0c221145bcfe9bfc129d9098e"><td class="mdescLeft">&#160;</td><td class="mdescRight">single-qualifier floating-point matrix with 4 x 2 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga308b9af0c221145bcfe9bfc129d9098e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac0a51b4812038aa81d73ffcc37f741ac"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gac0a51b4812038aa81d73ffcc37f741ac"></a>
typedef mat&lt; 4, 3, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gac0a51b4812038aa81d73ffcc37f741ac">float4x3</a></td></tr>
<tr class="memdesc:gac0a51b4812038aa81d73ffcc37f741ac"><td class="mdescLeft">&#160;</td><td class="mdescRight">single-qualifier floating-point matrix with 4 x 3 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gac0a51b4812038aa81d73ffcc37f741ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad3051649b3715d828a4ab92cdae7c3bf"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gad3051649b3715d828a4ab92cdae7c3bf"></a>
typedef mat&lt; 4, 4, float, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gad3051649b3715d828a4ab92cdae7c3bf">float4x4</a></td></tr>
<tr class="memdesc:gad3051649b3715d828a4ab92cdae7c3bf"><td class="mdescLeft">&#160;</td><td class="mdescRight">single-qualifier floating-point matrix with 4 x 4 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gad3051649b3715d828a4ab92cdae7c3bf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0670a2111b5e4a6410bd027fa0232fc3"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga0670a2111b5e4a6410bd027fa0232fc3"></a>
typedef int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga0670a2111b5e4a6410bd027fa0232fc3">int1</a></td></tr>
<tr class="memdesc:ga0670a2111b5e4a6410bd027fa0232fc3"><td class="mdescLeft">&#160;</td><td class="mdescRight">integer vector with 1 component. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga0670a2111b5e4a6410bd027fa0232fc3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga056ffe02d3a45af626f8e62221881c7a"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga056ffe02d3a45af626f8e62221881c7a"></a>
typedef int&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga056ffe02d3a45af626f8e62221881c7a">int1x1</a></td></tr>
<tr class="memdesc:ga056ffe02d3a45af626f8e62221881c7a"><td class="mdescLeft">&#160;</td><td class="mdescRight">integer matrix with 1 component. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga056ffe02d3a45af626f8e62221881c7a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafe3a8fd56354caafe24bfe1b1e3ad22a"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gafe3a8fd56354caafe24bfe1b1e3ad22a"></a>
typedef vec&lt; 2, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gafe3a8fd56354caafe24bfe1b1e3ad22a">int2</a></td></tr>
<tr class="memdesc:gafe3a8fd56354caafe24bfe1b1e3ad22a"><td class="mdescLeft">&#160;</td><td class="mdescRight">integer vector with 2 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gafe3a8fd56354caafe24bfe1b1e3ad22a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4e5ce477c15836b21e3c42daac68554d"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga4e5ce477c15836b21e3c42daac68554d"></a>
typedef mat&lt; 2, 2, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga4e5ce477c15836b21e3c42daac68554d">int2x2</a></td></tr>
<tr class="memdesc:ga4e5ce477c15836b21e3c42daac68554d"><td class="mdescLeft">&#160;</td><td class="mdescRight">integer matrix with 2 x 2 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga4e5ce477c15836b21e3c42daac68554d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga197ded5ad8354f6b6fb91189d7a269b3"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga197ded5ad8354f6b6fb91189d7a269b3"></a>
typedef mat&lt; 2, 3, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga197ded5ad8354f6b6fb91189d7a269b3">int2x3</a></td></tr>
<tr class="memdesc:ga197ded5ad8354f6b6fb91189d7a269b3"><td class="mdescLeft">&#160;</td><td class="mdescRight">integer matrix with 2 x 3 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga197ded5ad8354f6b6fb91189d7a269b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2749d59a7fddbac44f34ba78e57ef807"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga2749d59a7fddbac44f34ba78e57ef807"></a>
typedef mat&lt; 2, 4, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga2749d59a7fddbac44f34ba78e57ef807">int2x4</a></td></tr>
<tr class="memdesc:ga2749d59a7fddbac44f34ba78e57ef807"><td class="mdescLeft">&#160;</td><td class="mdescRight">integer matrix with 2 x 4 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga2749d59a7fddbac44f34ba78e57ef807"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga909c38a425f215a50c847145d7da09f0"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga909c38a425f215a50c847145d7da09f0"></a>
typedef vec&lt; 3, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga909c38a425f215a50c847145d7da09f0">int3</a></td></tr>
<tr class="memdesc:ga909c38a425f215a50c847145d7da09f0"><td class="mdescLeft">&#160;</td><td class="mdescRight">integer vector with 3 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga909c38a425f215a50c847145d7da09f0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa4cbe16a92cf3664376c7a2fc5126aa8"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaa4cbe16a92cf3664376c7a2fc5126aa8"></a>
typedef mat&lt; 3, 2, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gaa4cbe16a92cf3664376c7a2fc5126aa8">int3x2</a></td></tr>
<tr class="memdesc:gaa4cbe16a92cf3664376c7a2fc5126aa8"><td class="mdescLeft">&#160;</td><td class="mdescRight">integer matrix with 3 x 2 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gaa4cbe16a92cf3664376c7a2fc5126aa8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga15c9649286f0bf431bdf9b3509580048"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga15c9649286f0bf431bdf9b3509580048"></a>
typedef mat&lt; 3, 3, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga15c9649286f0bf431bdf9b3509580048">int3x3</a></td></tr>
<tr class="memdesc:ga15c9649286f0bf431bdf9b3509580048"><td class="mdescLeft">&#160;</td><td class="mdescRight">integer matrix with 3 x 3 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga15c9649286f0bf431bdf9b3509580048"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaacac46ddc7d15d0f9529d05c92946a0f"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaacac46ddc7d15d0f9529d05c92946a0f"></a>
typedef mat&lt; 3, 4, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gaacac46ddc7d15d0f9529d05c92946a0f">int3x4</a></td></tr>
<tr class="memdesc:gaacac46ddc7d15d0f9529d05c92946a0f"><td class="mdescLeft">&#160;</td><td class="mdescRight">integer matrix with 3 x 4 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gaacac46ddc7d15d0f9529d05c92946a0f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaecdef18c819c205aeee9f94dc93de56a"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gaecdef18c819c205aeee9f94dc93de56a"></a>
typedef vec&lt; 4, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gaecdef18c819c205aeee9f94dc93de56a">int4</a></td></tr>
<tr class="memdesc:gaecdef18c819c205aeee9f94dc93de56a"><td class="mdescLeft">&#160;</td><td class="mdescRight">integer vector with 4 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gaecdef18c819c205aeee9f94dc93de56a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga97a39dd9bc7d572810d80b8467cbffa1"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga97a39dd9bc7d572810d80b8467cbffa1"></a>
typedef mat&lt; 4, 2, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga97a39dd9bc7d572810d80b8467cbffa1">int4x2</a></td></tr>
<tr class="memdesc:ga97a39dd9bc7d572810d80b8467cbffa1"><td class="mdescLeft">&#160;</td><td class="mdescRight">integer matrix with 4 x 2 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga97a39dd9bc7d572810d80b8467cbffa1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae4a2c53f14aeec9a17c2b81142b7e82d"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="gae4a2c53f14aeec9a17c2b81142b7e82d"></a>
typedef mat&lt; 4, 3, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#gae4a2c53f14aeec9a17c2b81142b7e82d">int4x3</a></td></tr>
<tr class="memdesc:gae4a2c53f14aeec9a17c2b81142b7e82d"><td class="mdescLeft">&#160;</td><td class="mdescRight">integer matrix with 4 x 3 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:gae4a2c53f14aeec9a17c2b81142b7e82d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga04dee1552424198b8f58b377c2ee00d8"><td class="memItemLeft" align="right" valign="top"><a class="anchor" id="ga04dee1552424198b8f58b377c2ee00d8"></a>
typedef mat&lt; 4, 4, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00315.html#ga04dee1552424198b8f58b377c2ee00d8">int4x4</a></td></tr>
<tr class="memdesc:ga04dee1552424198b8f58b377c2ee00d8"><td class="mdescLeft">&#160;</td><td class="mdescRight">integer matrix with 4 x 4 components. (From GLM_GTX_compatibility extension) <br /></td></tr>
<tr class="separator:ga04dee1552424198b8f58b377c2ee00d8"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table><table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gac63011205bf6d0be82589dc56dd26708"><td class="memTemplParams" colspan="2"><a class="anchor" id="gac63011205bf6d0be82589dc56dd26708"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gac63011205bf6d0be82589dc56dd26708"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00315.html#gac63011205bf6d0be82589dc56dd26708">atan2</a> (T x, T y)</td></tr>
<tr class="memdesc:gac63011205bf6d0be82589dc56dd26708"><td class="mdescLeft">&#160;</td><td class="mdescRight">Arc tangent. Returns an angle whose tangent is y/x. The signs of x and y are used to determine what quadrant the angle is in. The range of values returned by this function is [-PI, PI]. Results are undefined if x and y are both 0. (From GLM_GTX_compatibility) <br /></td></tr>
<tr class="separator:gac63011205bf6d0be82589dc56dd26708"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga83bc41bd6f89113ee8006576b12bfc50"><td class="memTemplParams" colspan="2"><a class="anchor" id="ga83bc41bd6f89113ee8006576b12bfc50"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga83bc41bd6f89113ee8006576b12bfc50"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER vec&lt; 2, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00315.html#ga83bc41bd6f89113ee8006576b12bfc50">atan2</a> (const vec&lt; 2, T, Q &gt; &amp;x, const vec&lt; 2, T, Q &gt; &amp;y)</td></tr>
<tr class="memdesc:ga83bc41bd6f89113ee8006576b12bfc50"><td class="mdescLeft">&#160;</td><td class="mdescRight">Arc tangent. Returns an angle whose tangent is y/x. The signs of x and y are used to determine what quadrant the angle is in. The range of values returned by this function is [-PI, PI]. Results are undefined if x and y are both 0. (From GLM_GTX_compatibility) <br /></td></tr>
<tr class="separator:ga83bc41bd6f89113ee8006576b12bfc50"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac39314f5087e7e51e592897cabbc1927"><td class="memTemplParams" colspan="2"><a class="anchor" id="gac39314f5087e7e51e592897cabbc1927"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gac39314f5087e7e51e592897cabbc1927"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00315.html#gac39314f5087e7e51e592897cabbc1927">atan2</a> (const vec&lt; 3, T, Q &gt; &amp;x, const vec&lt; 3, T, Q &gt; &amp;y)</td></tr>
<tr class="memdesc:gac39314f5087e7e51e592897cabbc1927"><td class="mdescLeft">&#160;</td><td class="mdescRight">Arc tangent. Returns an angle whose tangent is y/x. The signs of x and y are used to determine what quadrant the angle is in. The range of values returned by this function is [-PI, PI]. Results are undefined if x and y are both 0. (From GLM_GTX_compatibility) <br /></td></tr>
<tr class="separator:gac39314f5087e7e51e592897cabbc1927"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaba86c28da7bf5bdac64fecf7d56e8ff3"><td class="memTemplParams" colspan="2"><a class="anchor" id="gaba86c28da7bf5bdac64fecf7d56e8ff3"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaba86c28da7bf5bdac64fecf7d56e8ff3"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER vec&lt; 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00315.html#gaba86c28da7bf5bdac64fecf7d56e8ff3">atan2</a> (const vec&lt; 4, T, Q &gt; &amp;x, const vec&lt; 4, T, Q &gt; &amp;y)</td></tr>
<tr class="memdesc:gaba86c28da7bf5bdac64fecf7d56e8ff3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Arc tangent. Returns an angle whose tangent is y/x. The signs of x and y are used to determine what quadrant the angle is in. The range of values returned by this function is [-PI, PI]. Results are undefined if x and y are both 0. (From GLM_GTX_compatibility) <br /></td></tr>
<tr class="separator:gaba86c28da7bf5bdac64fecf7d56e8ff3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf4b04dcd3526996d68c1bfe17bfc8657"><td class="memTemplParams" colspan="2"><a class="anchor" id="gaf4b04dcd3526996d68c1bfe17bfc8657"></a>
template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gaf4b04dcd3526996d68c1bfe17bfc8657"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00315.html#gaf4b04dcd3526996d68c1bfe17bfc8657">isfinite</a> (genType const &amp;x)</td></tr>
<tr class="memdesc:gaf4b04dcd3526996d68c1bfe17bfc8657"><td class="mdescLeft">&#160;</td><td class="mdescRight">Test whether or not a scalar or each vector component is a finite value. (From GLM_GTX_compatibility) <br /></td></tr>
<tr class="separator:gaf4b04dcd3526996d68c1bfe17bfc8657"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac3b12b8ac3014418fe53c299478b6603"><td class="memTemplParams" colspan="2"><a class="anchor" id="gac3b12b8ac3014418fe53c299478b6603"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gac3b12b8ac3014418fe53c299478b6603"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 1, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00315.html#gac3b12b8ac3014418fe53c299478b6603">isfinite</a> (const vec&lt; 1, T, Q &gt; &amp;x)</td></tr>
<tr class="memdesc:gac3b12b8ac3014418fe53c299478b6603"><td class="mdescLeft">&#160;</td><td class="mdescRight">Test whether or not a scalar or each vector component is a finite value. (From GLM_GTX_compatibility) <br /></td></tr>
<tr class="separator:gac3b12b8ac3014418fe53c299478b6603"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8e76dc3e406ce6a4155c2b12a2e4b084"><td class="memTemplParams" colspan="2"><a class="anchor" id="ga8e76dc3e406ce6a4155c2b12a2e4b084"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga8e76dc3e406ce6a4155c2b12a2e4b084"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 2, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00315.html#ga8e76dc3e406ce6a4155c2b12a2e4b084">isfinite</a> (const vec&lt; 2, T, Q &gt; &amp;x)</td></tr>
<tr class="memdesc:ga8e76dc3e406ce6a4155c2b12a2e4b084"><td class="mdescLeft">&#160;</td><td class="mdescRight">Test whether or not a scalar or each vector component is a finite value. (From GLM_GTX_compatibility) <br /></td></tr>
<tr class="separator:ga8e76dc3e406ce6a4155c2b12a2e4b084"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga929ef27f896d902c1771a2e5e150fc97"><td class="memTemplParams" colspan="2"><a class="anchor" id="ga929ef27f896d902c1771a2e5e150fc97"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga929ef27f896d902c1771a2e5e150fc97"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00315.html#ga929ef27f896d902c1771a2e5e150fc97">isfinite</a> (const vec&lt; 3, T, Q &gt; &amp;x)</td></tr>
<tr class="memdesc:ga929ef27f896d902c1771a2e5e150fc97"><td class="mdescLeft">&#160;</td><td class="mdescRight">Test whether or not a scalar or each vector component is a finite value. (From GLM_GTX_compatibility) <br /></td></tr>
<tr class="separator:ga929ef27f896d902c1771a2e5e150fc97"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga19925badbe10ce61df1d0de00be0b5ad"><td class="memTemplParams" colspan="2"><a class="anchor" id="ga19925badbe10ce61df1d0de00be0b5ad"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga19925badbe10ce61df1d0de00be0b5ad"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00315.html#ga19925badbe10ce61df1d0de00be0b5ad">isfinite</a> (const vec&lt; 4, T, Q &gt; &amp;x)</td></tr>
<tr class="memdesc:ga19925badbe10ce61df1d0de00be0b5ad"><td class="mdescLeft">&#160;</td><td class="mdescRight">Test whether or not a scalar or each vector component is a finite value. (From GLM_GTX_compatibility) <br /></td></tr>
<tr class="separator:ga19925badbe10ce61df1d0de00be0b5ad"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5494ba3a95ea6594c86fc75236886864"><td class="memTemplParams" colspan="2"><a class="anchor" id="ga5494ba3a95ea6594c86fc75236886864"></a>
template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga5494ba3a95ea6594c86fc75236886864"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00315.html#ga5494ba3a95ea6594c86fc75236886864">lerp</a> (T x, T y, T a)</td></tr>
<tr class="memdesc:ga5494ba3a95ea6594c86fc75236886864"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns x * (1.0 - a) + y * a, i.e., the linear blend of x and y using the floating-point value a. The value for a is not restricted to the range [0, 1]. (From GLM_GTX_compatibility) <br /></td></tr>
<tr class="separator:ga5494ba3a95ea6594c86fc75236886864"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa551c0a0e16d2d4608e49f7696df897f"><td class="memTemplParams" colspan="2"><a class="anchor" id="gaa551c0a0e16d2d4608e49f7696df897f"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa551c0a0e16d2d4608e49f7696df897f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER vec&lt; 2, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00315.html#gaa551c0a0e16d2d4608e49f7696df897f">lerp</a> (const vec&lt; 2, T, Q &gt; &amp;x, const vec&lt; 2, T, Q &gt; &amp;y, T a)</td></tr>
<tr class="memdesc:gaa551c0a0e16d2d4608e49f7696df897f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns x * (1.0 - a) + y * a, i.e., the linear blend of x and y using the floating-point value a. The value for a is not restricted to the range [0, 1]. (From GLM_GTX_compatibility) <br /></td></tr>
<tr class="separator:gaa551c0a0e16d2d4608e49f7696df897f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga44a8b5fd776320f1713413dec959b32a"><td class="memTemplParams" colspan="2"><a class="anchor" id="ga44a8b5fd776320f1713413dec959b32a"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga44a8b5fd776320f1713413dec959b32a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00315.html#ga44a8b5fd776320f1713413dec959b32a">lerp</a> (const vec&lt; 3, T, Q &gt; &amp;x, const vec&lt; 3, T, Q &gt; &amp;y, T a)</td></tr>
<tr class="memdesc:ga44a8b5fd776320f1713413dec959b32a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns x * (1.0 - a) + y * a, i.e., the linear blend of x and y using the floating-point value a. The value for a is not restricted to the range [0, 1]. (From GLM_GTX_compatibility) <br /></td></tr>
<tr class="separator:ga44a8b5fd776320f1713413dec959b32a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga89ac8e000199292ec7875519d27e214b"><td class="memTemplParams" colspan="2"><a class="anchor" id="ga89ac8e000199292ec7875519d27e214b"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga89ac8e000199292ec7875519d27e214b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER vec&lt; 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00315.html#ga89ac8e000199292ec7875519d27e214b">lerp</a> (const vec&lt; 4, T, Q &gt; &amp;x, const vec&lt; 4, T, Q &gt; &amp;y, T a)</td></tr>
<tr class="memdesc:ga89ac8e000199292ec7875519d27e214b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns x * (1.0 - a) + y * a, i.e., the linear blend of x and y using the floating-point value a. The value for a is not restricted to the range [0, 1]. (From GLM_GTX_compatibility) <br /></td></tr>
<tr class="separator:ga89ac8e000199292ec7875519d27e214b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf68de5baf72d16135368b8ef4f841604"><td class="memTemplParams" colspan="2"><a class="anchor" id="gaf68de5baf72d16135368b8ef4f841604"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf68de5baf72d16135368b8ef4f841604"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER vec&lt; 2, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00315.html#gaf68de5baf72d16135368b8ef4f841604">lerp</a> (const vec&lt; 2, T, Q &gt; &amp;x, const vec&lt; 2, T, Q &gt; &amp;y, const vec&lt; 2, T, Q &gt; &amp;a)</td></tr>
<tr class="memdesc:gaf68de5baf72d16135368b8ef4f841604"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise result of x * (1.0 - a) + y * a, i.e., the linear blend of x and y using vector a. The value for a is not restricted to the range [0, 1]. (From GLM_GTX_compatibility) <br /></td></tr>
<tr class="separator:gaf68de5baf72d16135368b8ef4f841604"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4ae1a616c8540a2649eab8e0cd051bb3"><td class="memTemplParams" colspan="2"><a class="anchor" id="ga4ae1a616c8540a2649eab8e0cd051bb3"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga4ae1a616c8540a2649eab8e0cd051bb3"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00315.html#ga4ae1a616c8540a2649eab8e0cd051bb3">lerp</a> (const vec&lt; 3, T, Q &gt; &amp;x, const vec&lt; 3, T, Q &gt; &amp;y, const vec&lt; 3, T, Q &gt; &amp;a)</td></tr>
<tr class="memdesc:ga4ae1a616c8540a2649eab8e0cd051bb3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise result of x * (1.0 - a) + y * a, i.e., the linear blend of x and y using vector a. The value for a is not restricted to the range [0, 1]. (From GLM_GTX_compatibility) <br /></td></tr>
<tr class="separator:ga4ae1a616c8540a2649eab8e0cd051bb3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab5477ab69c40de4db5d58d3359529724"><td class="memTemplParams" colspan="2"><a class="anchor" id="gab5477ab69c40de4db5d58d3359529724"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gab5477ab69c40de4db5d58d3359529724"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER vec&lt; 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00315.html#gab5477ab69c40de4db5d58d3359529724">lerp</a> (const vec&lt; 4, T, Q &gt; &amp;x, const vec&lt; 4, T, Q &gt; &amp;y, const vec&lt; 4, T, Q &gt; &amp;a)</td></tr>
<tr class="memdesc:gab5477ab69c40de4db5d58d3359529724"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise result of x * (1.0 - a) + y * a, i.e., the linear blend of x and y using vector a. The value for a is not restricted to the range [0, 1]. (From GLM_GTX_compatibility) <br /></td></tr>
<tr class="separator:gab5477ab69c40de4db5d58d3359529724"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0fd09e616d122bc2ed9726682ffd44b7"><td class="memTemplParams" colspan="2"><a class="anchor" id="ga0fd09e616d122bc2ed9726682ffd44b7"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga0fd09e616d122bc2ed9726682ffd44b7"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00315.html#ga0fd09e616d122bc2ed9726682ffd44b7">saturate</a> (T x)</td></tr>
<tr class="memdesc:ga0fd09e616d122bc2ed9726682ffd44b7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns clamp(x, 0, 1) for each component in x. (From GLM_GTX_compatibility) <br /></td></tr>
<tr class="separator:ga0fd09e616d122bc2ed9726682ffd44b7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaee97b8001c794a78a44f5d59f62a8aba"><td class="memTemplParams" colspan="2"><a class="anchor" id="gaee97b8001c794a78a44f5d59f62a8aba"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaee97b8001c794a78a44f5d59f62a8aba"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER vec&lt; 2, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00315.html#gaee97b8001c794a78a44f5d59f62a8aba">saturate</a> (const vec&lt; 2, T, Q &gt; &amp;x)</td></tr>
<tr class="memdesc:gaee97b8001c794a78a44f5d59f62a8aba"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns clamp(x, 0, 1) for each component in x. (From GLM_GTX_compatibility) <br /></td></tr>
<tr class="separator:gaee97b8001c794a78a44f5d59f62a8aba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga39bfe3a421286ee31680d45c31ccc161"><td class="memTemplParams" colspan="2"><a class="anchor" id="ga39bfe3a421286ee31680d45c31ccc161"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga39bfe3a421286ee31680d45c31ccc161"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00315.html#ga39bfe3a421286ee31680d45c31ccc161">saturate</a> (const vec&lt; 3, T, Q &gt; &amp;x)</td></tr>
<tr class="memdesc:ga39bfe3a421286ee31680d45c31ccc161"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns clamp(x, 0, 1) for each component in x. (From GLM_GTX_compatibility) <br /></td></tr>
<tr class="separator:ga39bfe3a421286ee31680d45c31ccc161"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga356f8c3a7e7d6376d3d4b0a026407183"><td class="memTemplParams" colspan="2"><a class="anchor" id="ga356f8c3a7e7d6376d3d4b0a026407183"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga356f8c3a7e7d6376d3d4b0a026407183"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER vec&lt; 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00315.html#ga356f8c3a7e7d6376d3d4b0a026407183">saturate</a> (const vec&lt; 4, T, Q &gt; &amp;x)</td></tr>
<tr class="memdesc:ga356f8c3a7e7d6376d3d4b0a026407183"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns clamp(x, 0, 1) for each component in x. (From GLM_GTX_compatibility) <br /></td></tr>
<tr class="separator:ga356f8c3a7e7d6376d3d4b0a026407183"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00017.html" title="GLM_GTX_compatibility ">glm/gtx/compatibility.hpp</a>&gt; to use the features of this extension. </p>
<p>Provide functions to increase the compatibility with Cg and HLSL languages </p>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
