<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: type_ptr.hpp Source File</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li class="current"><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
  <div id="navrow2" class="tabs2">
    <ul class="tablist">
      <li><a href="files.html"><span>File&#160;List</span></a></li>
    </ul>
  </div>
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div id="nav-path" class="navpath">
  <ul>
<li class="navelem"><a class="el" href="dir_3a581ba30d25676e4b797b1f96d53b45.html">F:</a></li><li class="navelem"><a class="el" href="dir_9e5fe034a00e89334fd5186c3e7db156.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_d9496f0844b48bc7e53b5af8c99b9ab2.html">Source</a></li><li class="navelem"><a class="el" href="dir_a8bee7be44182a33f3820393ae0b105d.html">G-Truc</a></li><li class="navelem"><a class="el" href="dir_44e5e654415abd9ca6fdeaddaff8565e.html">glm</a></li><li class="navelem"><a class="el" href="dir_cef2d71d502cb69a9252bca2297d9549.html">glm</a></li><li class="navelem"><a class="el" href="dir_4c6bd29c73fa4e5a2509e1c15f846751.html">gtc</a></li>  </ul>
</div>
</div><!-- top -->
<div class="header">
  <div class="headertitle">
<div class="title">type_ptr.hpp</div>  </div>
</div><!--header-->
<div class="contents">
<a href="a00175.html">Go to the documentation of this file.</a><div class="fragment"><div class="line"><a name="l00001"></a><span class="lineno">    1</span>&#160;</div>
<div class="line"><a name="l00034"></a><span class="lineno">   34</span>&#160;<span class="preprocessor">#pragma once</span></div>
<div class="line"><a name="l00035"></a><span class="lineno">   35</span>&#160;</div>
<div class="line"><a name="l00036"></a><span class="lineno">   36</span>&#160;<span class="comment">// Dependency:</span></div>
<div class="line"><a name="l00037"></a><span class="lineno">   37</span>&#160;<span class="preprocessor">#include &quot;../gtc/quaternion.hpp&quot;</span></div>
<div class="line"><a name="l00038"></a><span class="lineno">   38</span>&#160;<span class="preprocessor">#include &quot;../gtc/vec1.hpp&quot;</span></div>
<div class="line"><a name="l00039"></a><span class="lineno">   39</span>&#160;<span class="preprocessor">#include &quot;../vec2.hpp&quot;</span></div>
<div class="line"><a name="l00040"></a><span class="lineno">   40</span>&#160;<span class="preprocessor">#include &quot;../vec3.hpp&quot;</span></div>
<div class="line"><a name="l00041"></a><span class="lineno">   41</span>&#160;<span class="preprocessor">#include &quot;../vec4.hpp&quot;</span></div>
<div class="line"><a name="l00042"></a><span class="lineno">   42</span>&#160;<span class="preprocessor">#include &quot;../mat2x2.hpp&quot;</span></div>
<div class="line"><a name="l00043"></a><span class="lineno">   43</span>&#160;<span class="preprocessor">#include &quot;../mat2x3.hpp&quot;</span></div>
<div class="line"><a name="l00044"></a><span class="lineno">   44</span>&#160;<span class="preprocessor">#include &quot;../mat2x4.hpp&quot;</span></div>
<div class="line"><a name="l00045"></a><span class="lineno">   45</span>&#160;<span class="preprocessor">#include &quot;../mat3x2.hpp&quot;</span></div>
<div class="line"><a name="l00046"></a><span class="lineno">   46</span>&#160;<span class="preprocessor">#include &quot;../mat3x3.hpp&quot;</span></div>
<div class="line"><a name="l00047"></a><span class="lineno">   47</span>&#160;<span class="preprocessor">#include &quot;../mat3x4.hpp&quot;</span></div>
<div class="line"><a name="l00048"></a><span class="lineno">   48</span>&#160;<span class="preprocessor">#include &quot;../mat4x2.hpp&quot;</span></div>
<div class="line"><a name="l00049"></a><span class="lineno">   49</span>&#160;<span class="preprocessor">#include &quot;../mat4x3.hpp&quot;</span></div>
<div class="line"><a name="l00050"></a><span class="lineno">   50</span>&#160;<span class="preprocessor">#include &quot;../mat4x4.hpp&quot;</span></div>
<div class="line"><a name="l00051"></a><span class="lineno">   51</span>&#160;<span class="preprocessor">#include &lt;cstring&gt;</span></div>
<div class="line"><a name="l00052"></a><span class="lineno">   52</span>&#160;</div>
<div class="line"><a name="l00053"></a><span class="lineno">   53</span>&#160;<span class="preprocessor">#if GLM_MESSAGES == GLM_ENABLE &amp;&amp; !defined(GLM_EXT_INCLUDED)</span></div>
<div class="line"><a name="l00054"></a><span class="lineno">   54</span>&#160;<span class="preprocessor">#       pragma message(&quot;GLM: GLM_GTC_type_ptr extension included&quot;)</span></div>
<div class="line"><a name="l00055"></a><span class="lineno">   55</span>&#160;<span class="preprocessor">#endif</span></div>
<div class="line"><a name="l00056"></a><span class="lineno">   56</span>&#160;</div>
<div class="line"><a name="l00057"></a><span class="lineno">   57</span>&#160;<span class="keyword">namespace </span><a class="code" href="a00236.html">glm</a></div>
<div class="line"><a name="l00058"></a><span class="lineno">   58</span>&#160;{</div>
<div class="line"><a name="l00061"></a><span class="lineno">   61</span>&#160;</div>
<div class="line"><a name="l00064"></a><span class="lineno">   64</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> genType&gt;</div>
<div class="line"><a name="l00065"></a><span class="lineno">   65</span>&#160;        GLM_FUNC_DECL <span class="keyword">typename</span> genType::value_type <span class="keyword">const</span> * <a class="code" href="a00305.html#ga1c64669e1ba1160ad9386e43dc57569a">value_ptr</a>(genType <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00066"></a><span class="lineno">   66</span>&#160;</div>
<div class="line"><a name="l00069"></a><span class="lineno">   69</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00070"></a><span class="lineno">   70</span>&#160;        GLM_FUNC_DECL vec&lt;1, T, Q&gt; <a class="code" href="a00305.html#ga6af06bb60d64ca8bcd169e3c93bc2419">make_vec1</a>(vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00071"></a><span class="lineno">   71</span>&#160;</div>
<div class="line"><a name="l00074"></a><span class="lineno">   74</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00075"></a><span class="lineno">   75</span>&#160;        GLM_FUNC_DECL vec&lt;1, T, Q&gt; <a class="code" href="a00305.html#ga6af06bb60d64ca8bcd169e3c93bc2419">make_vec1</a>(vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00076"></a><span class="lineno">   76</span>&#160;</div>
<div class="line"><a name="l00079"></a><span class="lineno">   79</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00080"></a><span class="lineno">   80</span>&#160;        GLM_FUNC_DECL vec&lt;1, T, Q&gt; <a class="code" href="a00305.html#ga6af06bb60d64ca8bcd169e3c93bc2419">make_vec1</a>(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00081"></a><span class="lineno">   81</span>&#160;</div>
<div class="line"><a name="l00084"></a><span class="lineno">   84</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00085"></a><span class="lineno">   85</span>&#160;        GLM_FUNC_DECL vec&lt;1, T, Q&gt; <a class="code" href="a00305.html#ga6af06bb60d64ca8bcd169e3c93bc2419">make_vec1</a>(vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00086"></a><span class="lineno">   86</span>&#160;</div>
<div class="line"><a name="l00089"></a><span class="lineno">   89</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00090"></a><span class="lineno">   90</span>&#160;        GLM_FUNC_DECL vec&lt;2, T, Q&gt; <a class="code" href="a00305.html#ga81253cf7b0ebfbb1e70540c5774e6824">make_vec2</a>(vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00091"></a><span class="lineno">   91</span>&#160;</div>
<div class="line"><a name="l00094"></a><span class="lineno">   94</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00095"></a><span class="lineno">   95</span>&#160;        GLM_FUNC_DECL vec&lt;2, T, Q&gt; <a class="code" href="a00305.html#ga81253cf7b0ebfbb1e70540c5774e6824">make_vec2</a>(vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00096"></a><span class="lineno">   96</span>&#160;</div>
<div class="line"><a name="l00099"></a><span class="lineno">   99</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00100"></a><span class="lineno">  100</span>&#160;        GLM_FUNC_DECL vec&lt;2, T, Q&gt; <a class="code" href="a00305.html#ga81253cf7b0ebfbb1e70540c5774e6824">make_vec2</a>(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00101"></a><span class="lineno">  101</span>&#160;</div>
<div class="line"><a name="l00104"></a><span class="lineno">  104</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00105"></a><span class="lineno">  105</span>&#160;        GLM_FUNC_DECL vec&lt;2, T, Q&gt; <a class="code" href="a00305.html#ga81253cf7b0ebfbb1e70540c5774e6824">make_vec2</a>(vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00106"></a><span class="lineno">  106</span>&#160;</div>
<div class="line"><a name="l00109"></a><span class="lineno">  109</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00110"></a><span class="lineno">  110</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; <a class="code" href="a00305.html#gad9e0d36ff489cb30c65ad1fa40351651">make_vec3</a>(vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00111"></a><span class="lineno">  111</span>&#160;</div>
<div class="line"><a name="l00114"></a><span class="lineno">  114</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00115"></a><span class="lineno">  115</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; <a class="code" href="a00305.html#gad9e0d36ff489cb30c65ad1fa40351651">make_vec3</a>(vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00116"></a><span class="lineno">  116</span>&#160;</div>
<div class="line"><a name="l00119"></a><span class="lineno">  119</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00120"></a><span class="lineno">  120</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; <a class="code" href="a00305.html#gad9e0d36ff489cb30c65ad1fa40351651">make_vec3</a>(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00121"></a><span class="lineno">  121</span>&#160;</div>
<div class="line"><a name="l00124"></a><span class="lineno">  124</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00125"></a><span class="lineno">  125</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, Q&gt; <a class="code" href="a00305.html#gad9e0d36ff489cb30c65ad1fa40351651">make_vec3</a>(vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00126"></a><span class="lineno">  126</span>&#160;</div>
<div class="line"><a name="l00129"></a><span class="lineno">  129</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00130"></a><span class="lineno">  130</span>&#160;        GLM_FUNC_DECL vec&lt;4, T, Q&gt; <a class="code" href="a00305.html#ga63f576518993efc22a969f18f80e29bb">make_vec4</a>(vec&lt;1, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00131"></a><span class="lineno">  131</span>&#160;</div>
<div class="line"><a name="l00134"></a><span class="lineno">  134</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00135"></a><span class="lineno">  135</span>&#160;        GLM_FUNC_DECL vec&lt;4, T, Q&gt; <a class="code" href="a00305.html#ga63f576518993efc22a969f18f80e29bb">make_vec4</a>(vec&lt;2, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00136"></a><span class="lineno">  136</span>&#160;</div>
<div class="line"><a name="l00139"></a><span class="lineno">  139</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00140"></a><span class="lineno">  140</span>&#160;        GLM_FUNC_DECL vec&lt;4, T, Q&gt; <a class="code" href="a00305.html#ga63f576518993efc22a969f18f80e29bb">make_vec4</a>(vec&lt;3, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00141"></a><span class="lineno">  141</span>&#160;</div>
<div class="line"><a name="l00144"></a><span class="lineno">  144</span>&#160;        <span class="keyword">template</span> &lt;<span class="keyword">typename</span> T, qualifier Q&gt;</div>
<div class="line"><a name="l00145"></a><span class="lineno">  145</span>&#160;        GLM_FUNC_DECL vec&lt;4, T, Q&gt; <a class="code" href="a00305.html#ga63f576518993efc22a969f18f80e29bb">make_vec4</a>(vec&lt;4, T, Q&gt; <span class="keyword">const</span>&amp; v);</div>
<div class="line"><a name="l00146"></a><span class="lineno">  146</span>&#160;</div>
<div class="line"><a name="l00149"></a><span class="lineno">  149</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00150"></a><span class="lineno">  150</span>&#160;        GLM_FUNC_DECL vec&lt;2, T, defaultp&gt; <a class="code" href="a00305.html#ga81253cf7b0ebfbb1e70540c5774e6824">make_vec2</a>(T <span class="keyword">const</span> * <span class="keyword">const</span> ptr);</div>
<div class="line"><a name="l00151"></a><span class="lineno">  151</span>&#160;</div>
<div class="line"><a name="l00154"></a><span class="lineno">  154</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00155"></a><span class="lineno">  155</span>&#160;        GLM_FUNC_DECL vec&lt;3, T, defaultp&gt; <a class="code" href="a00305.html#gad9e0d36ff489cb30c65ad1fa40351651">make_vec3</a>(T <span class="keyword">const</span> * <span class="keyword">const</span> ptr);</div>
<div class="line"><a name="l00156"></a><span class="lineno">  156</span>&#160;</div>
<div class="line"><a name="l00159"></a><span class="lineno">  159</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00160"></a><span class="lineno">  160</span>&#160;        GLM_FUNC_DECL vec&lt;4, T, defaultp&gt; <a class="code" href="a00305.html#ga63f576518993efc22a969f18f80e29bb">make_vec4</a>(T <span class="keyword">const</span> * <span class="keyword">const</span> ptr);</div>
<div class="line"><a name="l00161"></a><span class="lineno">  161</span>&#160;</div>
<div class="line"><a name="l00164"></a><span class="lineno">  164</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00165"></a><span class="lineno">  165</span>&#160;        GLM_FUNC_DECL mat&lt;2, 2, T, defaultp&gt; <a class="code" href="a00305.html#gae49e1c7bcd5abec74d1c34155031f663">make_mat2x2</a>(T <span class="keyword">const</span> * <span class="keyword">const</span> ptr);</div>
<div class="line"><a name="l00166"></a><span class="lineno">  166</span>&#160;</div>
<div class="line"><a name="l00169"></a><span class="lineno">  169</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00170"></a><span class="lineno">  170</span>&#160;        GLM_FUNC_DECL mat&lt;2, 3, T, defaultp&gt; <a class="code" href="a00305.html#ga21982104164789cf8985483aaefc25e8">make_mat2x3</a>(T <span class="keyword">const</span> * <span class="keyword">const</span> ptr);</div>
<div class="line"><a name="l00171"></a><span class="lineno">  171</span>&#160;</div>
<div class="line"><a name="l00174"></a><span class="lineno">  174</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00175"></a><span class="lineno">  175</span>&#160;        GLM_FUNC_DECL mat&lt;2, 4, T, defaultp&gt; <a class="code" href="a00305.html#ga078b862c90b0e9a79ed43a58997d8388">make_mat2x4</a>(T <span class="keyword">const</span> * <span class="keyword">const</span> ptr);</div>
<div class="line"><a name="l00176"></a><span class="lineno">  176</span>&#160;</div>
<div class="line"><a name="l00179"></a><span class="lineno">  179</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00180"></a><span class="lineno">  180</span>&#160;        GLM_FUNC_DECL mat&lt;3, 2, T, defaultp&gt; <a class="code" href="a00305.html#ga27a24e121dc39e6857620e0f85b6e1a8">make_mat3x2</a>(T <span class="keyword">const</span> * <span class="keyword">const</span> ptr);</div>
<div class="line"><a name="l00181"></a><span class="lineno">  181</span>&#160;</div>
<div class="line"><a name="l00184"></a><span class="lineno">  184</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00185"></a><span class="lineno">  185</span>&#160;        GLM_FUNC_DECL mat&lt;3, 3, T, defaultp&gt; <a class="code" href="a00305.html#gaf2e8337b15c3362aaeb6e5849e1c0536">make_mat3x3</a>(T <span class="keyword">const</span> * <span class="keyword">const</span> ptr);</div>
<div class="line"><a name="l00186"></a><span class="lineno">  186</span>&#160;</div>
<div class="line"><a name="l00189"></a><span class="lineno">  189</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00190"></a><span class="lineno">  190</span>&#160;        GLM_FUNC_DECL mat&lt;3, 4, T, defaultp&gt; <a class="code" href="a00305.html#ga05dd66232aedb993e3b8e7b35eaf932b">make_mat3x4</a>(T <span class="keyword">const</span> * <span class="keyword">const</span> ptr);</div>
<div class="line"><a name="l00191"></a><span class="lineno">  191</span>&#160;</div>
<div class="line"><a name="l00194"></a><span class="lineno">  194</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00195"></a><span class="lineno">  195</span>&#160;        GLM_FUNC_DECL mat&lt;4, 2, T, defaultp&gt; <a class="code" href="a00305.html#ga8b34c9b25bf3310d8ff9c828c7e2d97c">make_mat4x2</a>(T <span class="keyword">const</span> * <span class="keyword">const</span> ptr);</div>
<div class="line"><a name="l00196"></a><span class="lineno">  196</span>&#160;</div>
<div class="line"><a name="l00199"></a><span class="lineno">  199</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00200"></a><span class="lineno">  200</span>&#160;        GLM_FUNC_DECL mat&lt;4, 3, T, defaultp&gt; <a class="code" href="a00305.html#ga0330bf6640092d7985fac92927bbd42b">make_mat4x3</a>(T <span class="keyword">const</span> * <span class="keyword">const</span> ptr);</div>
<div class="line"><a name="l00201"></a><span class="lineno">  201</span>&#160;</div>
<div class="line"><a name="l00204"></a><span class="lineno">  204</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00205"></a><span class="lineno">  205</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00305.html#ga8f084be30e404844bfbb4a551ac2728c">make_mat4x4</a>(T <span class="keyword">const</span> * <span class="keyword">const</span> ptr);</div>
<div class="line"><a name="l00206"></a><span class="lineno">  206</span>&#160;</div>
<div class="line"><a name="l00209"></a><span class="lineno">  209</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00210"></a><span class="lineno">  210</span>&#160;        GLM_FUNC_DECL mat&lt;2, 2, T, defaultp&gt; <a class="code" href="a00305.html#ga04409e74dc3da251d2501acf5b4b546c">make_mat2</a>(T <span class="keyword">const</span> * <span class="keyword">const</span> ptr);</div>
<div class="line"><a name="l00211"></a><span class="lineno">  211</span>&#160;</div>
<div class="line"><a name="l00214"></a><span class="lineno">  214</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00215"></a><span class="lineno">  215</span>&#160;        GLM_FUNC_DECL mat&lt;3, 3, T, defaultp&gt; <a class="code" href="a00305.html#ga611ee7c4d4cadfc83a8fa8e1d10a170f">make_mat3</a>(T <span class="keyword">const</span> * <span class="keyword">const</span> ptr);</div>
<div class="line"><a name="l00216"></a><span class="lineno">  216</span>&#160;</div>
<div class="line"><a name="l00219"></a><span class="lineno">  219</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00220"></a><span class="lineno">  220</span>&#160;        GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; <a class="code" href="a00305.html#gae7bcedb710d1446c87fd1fc93ed8ee9a">make_mat4</a>(T <span class="keyword">const</span> * <span class="keyword">const</span> ptr);</div>
<div class="line"><a name="l00221"></a><span class="lineno">  221</span>&#160;</div>
<div class="line"><a name="l00224"></a><span class="lineno">  224</span>&#160;        <span class="keyword">template</span>&lt;<span class="keyword">typename</span> T&gt;</div>
<div class="line"><a name="l00225"></a><span class="lineno">  225</span>&#160;        GLM_FUNC_DECL qua&lt;T, defaultp&gt; <a class="code" href="a00305.html#ga58110d7d81cf7d029e2bab7f8cd9b246">make_quat</a>(T <span class="keyword">const</span> * <span class="keyword">const</span> ptr);</div>
<div class="line"><a name="l00226"></a><span class="lineno">  226</span>&#160;</div>
<div class="line"><a name="l00228"></a><span class="lineno">  228</span>&#160;}<span class="comment">//namespace glm</span></div>
<div class="line"><a name="l00229"></a><span class="lineno">  229</span>&#160;</div>
<div class="line"><a name="l00230"></a><span class="lineno">  230</span>&#160;<span class="preprocessor">#include &quot;type_ptr.inl&quot;</span></div>
<div class="ttc" id="a00305_html_ga611ee7c4d4cadfc83a8fa8e1d10a170f"><div class="ttname"><a href="a00305.html#ga611ee7c4d4cadfc83a8fa8e1d10a170f">glm::make_mat3</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 3, 3, T, defaultp &gt; make_mat3(T const *const ptr)</div><div class="ttdoc">Build a matrix from a pointer. </div></div>
<div class="ttc" id="a00305_html_gad9e0d36ff489cb30c65ad1fa40351651"><div class="ttname"><a href="a00305.html#gad9e0d36ff489cb30c65ad1fa40351651">glm::make_vec3</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 3, T, defaultp &gt; make_vec3(T const *const ptr)</div><div class="ttdoc">Build a vector from a pointer. </div></div>
<div class="ttc" id="a00305_html_ga27a24e121dc39e6857620e0f85b6e1a8"><div class="ttname"><a href="a00305.html#ga27a24e121dc39e6857620e0f85b6e1a8">glm::make_mat3x2</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 3, 2, T, defaultp &gt; make_mat3x2(T const *const ptr)</div><div class="ttdoc">Build a matrix from a pointer. </div></div>
<div class="ttc" id="a00305_html_ga6af06bb60d64ca8bcd169e3c93bc2419"><div class="ttname"><a href="a00305.html#ga6af06bb60d64ca8bcd169e3c93bc2419">glm::make_vec1</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 1, T, Q &gt; make_vec1(vec&lt; 4, T, Q &gt; const &amp;v)</div><div class="ttdoc">Build a vector from a pointer. </div></div>
<div class="ttc" id="a00305_html_ga58110d7d81cf7d029e2bab7f8cd9b246"><div class="ttname"><a href="a00305.html#ga58110d7d81cf7d029e2bab7f8cd9b246">glm::make_quat</a></div><div class="ttdeci">GLM_FUNC_DECL qua&lt; T, defaultp &gt; make_quat(T const *const ptr)</div><div class="ttdoc">Build a quaternion from a pointer. </div></div>
<div class="ttc" id="a00305_html_gae7bcedb710d1446c87fd1fc93ed8ee9a"><div class="ttname"><a href="a00305.html#gae7bcedb710d1446c87fd1fc93ed8ee9a">glm::make_mat4</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; make_mat4(T const *const ptr)</div><div class="ttdoc">Build a matrix from a pointer. </div></div>
<div class="ttc" id="a00305_html_ga81253cf7b0ebfbb1e70540c5774e6824"><div class="ttname"><a href="a00305.html#ga81253cf7b0ebfbb1e70540c5774e6824">glm::make_vec2</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 2, T, defaultp &gt; make_vec2(T const *const ptr)</div><div class="ttdoc">Build a vector from a pointer. </div></div>
<div class="ttc" id="a00305_html_ga078b862c90b0e9a79ed43a58997d8388"><div class="ttname"><a href="a00305.html#ga078b862c90b0e9a79ed43a58997d8388">glm::make_mat2x4</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 2, 4, T, defaultp &gt; make_mat2x4(T const *const ptr)</div><div class="ttdoc">Build a matrix from a pointer. </div></div>
<div class="ttc" id="a00305_html_ga04409e74dc3da251d2501acf5b4b546c"><div class="ttname"><a href="a00305.html#ga04409e74dc3da251d2501acf5b4b546c">glm::make_mat2</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 2, 2, T, defaultp &gt; make_mat2(T const *const ptr)</div><div class="ttdoc">Build a matrix from a pointer. </div></div>
<div class="ttc" id="a00305_html_ga1c64669e1ba1160ad9386e43dc57569a"><div class="ttname"><a href="a00305.html#ga1c64669e1ba1160ad9386e43dc57569a">glm::value_ptr</a></div><div class="ttdeci">GLM_FUNC_DECL genType::value_type const * value_ptr(genType const &amp;v)</div><div class="ttdoc">Return the constant address to the data of the input parameter. </div></div>
<div class="ttc" id="a00305_html_gae49e1c7bcd5abec74d1c34155031f663"><div class="ttname"><a href="a00305.html#gae49e1c7bcd5abec74d1c34155031f663">glm::make_mat2x2</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 2, 2, T, defaultp &gt; make_mat2x2(T const *const ptr)</div><div class="ttdoc">Build a matrix from a pointer. </div></div>
<div class="ttc" id="a00305_html_ga21982104164789cf8985483aaefc25e8"><div class="ttname"><a href="a00305.html#ga21982104164789cf8985483aaefc25e8">glm::make_mat2x3</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 2, 3, T, defaultp &gt; make_mat2x3(T const *const ptr)</div><div class="ttdoc">Build a matrix from a pointer. </div></div>
<div class="ttc" id="a00305_html_ga05dd66232aedb993e3b8e7b35eaf932b"><div class="ttname"><a href="a00305.html#ga05dd66232aedb993e3b8e7b35eaf932b">glm::make_mat3x4</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 3, 4, T, defaultp &gt; make_mat3x4(T const *const ptr)</div><div class="ttdoc">Build a matrix from a pointer. </div></div>
<div class="ttc" id="a00305_html_ga63f576518993efc22a969f18f80e29bb"><div class="ttname"><a href="a00305.html#ga63f576518993efc22a969f18f80e29bb">glm::make_vec4</a></div><div class="ttdeci">GLM_FUNC_DECL vec&lt; 4, T, defaultp &gt; make_vec4(T const *const ptr)</div><div class="ttdoc">Build a vector from a pointer. </div></div>
<div class="ttc" id="a00305_html_ga0330bf6640092d7985fac92927bbd42b"><div class="ttname"><a href="a00305.html#ga0330bf6640092d7985fac92927bbd42b">glm::make_mat4x3</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 3, T, defaultp &gt; make_mat4x3(T const *const ptr)</div><div class="ttdoc">Build a matrix from a pointer. </div></div>
<div class="ttc" id="a00305_html_gaf2e8337b15c3362aaeb6e5849e1c0536"><div class="ttname"><a href="a00305.html#gaf2e8337b15c3362aaeb6e5849e1c0536">glm::make_mat3x3</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 3, 3, T, defaultp &gt; make_mat3x3(T const *const ptr)</div><div class="ttdoc">Build a matrix from a pointer. </div></div>
<div class="ttc" id="a00305_html_ga8f084be30e404844bfbb4a551ac2728c"><div class="ttname"><a href="a00305.html#ga8f084be30e404844bfbb4a551ac2728c">glm::make_mat4x4</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt; make_mat4x4(T const *const ptr)</div><div class="ttdoc">Build a matrix from a pointer. </div></div>
<div class="ttc" id="a00305_html_ga8b34c9b25bf3310d8ff9c828c7e2d97c"><div class="ttname"><a href="a00305.html#ga8b34c9b25bf3310d8ff9c828c7e2d97c">glm::make_mat4x2</a></div><div class="ttdeci">GLM_FUNC_DECL mat&lt; 4, 2, T, defaultp &gt; make_mat4x2(T const *const ptr)</div><div class="ttdoc">Build a matrix from a pointer. </div></div>
<div class="ttc" id="a00236_html"><div class="ttname"><a href="a00236.html">glm</a></div><div class="ttdef"><b>Definition:</b> <a href="a00015_source.html#l00020">common.hpp:20</a></div></div>
</div><!-- fragment --></div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
