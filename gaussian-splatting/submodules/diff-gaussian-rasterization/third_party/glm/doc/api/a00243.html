<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_EXT_matrix_clip_space</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_EXT_matrix_clip_space<div class="ingroups"><a class="el" href="a00285.html">Stable extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Defines functions that generate clip space transformation matrices.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga0bcd4542e0affc63a0b8c08fcb839ea9"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga0bcd4542e0affc63a0b8c08fcb839ea9"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#ga0bcd4542e0affc63a0b8c08fcb839ea9">frustum</a> (T left, T right, T bottom, T top, T near, T far)</td></tr>
<tr class="memdesc:ga0bcd4542e0affc63a0b8c08fcb839ea9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a frustum matrix with default handedness, using the default handedness and default near and far clip planes definition.  <a href="a00243.html#ga0bcd4542e0affc63a0b8c08fcb839ea9">More...</a><br /></td></tr>
<tr class="separator:ga0bcd4542e0affc63a0b8c08fcb839ea9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae4277c37f61d81da01bc9db14ea90296"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gae4277c37f61d81da01bc9db14ea90296"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#gae4277c37f61d81da01bc9db14ea90296">frustumLH</a> (T left, T right, T bottom, T top, T near, T far)</td></tr>
<tr class="memdesc:gae4277c37f61d81da01bc9db14ea90296"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a left handed frustum matrix.  <a href="a00243.html#gae4277c37f61d81da01bc9db14ea90296">More...</a><br /></td></tr>
<tr class="separator:gae4277c37f61d81da01bc9db14ea90296"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga259520cad03b3f8bca9417920035ed01"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga259520cad03b3f8bca9417920035ed01"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#ga259520cad03b3f8bca9417920035ed01">frustumLH_NO</a> (T left, T right, T bottom, T top, T near, T far)</td></tr>
<tr class="memdesc:ga259520cad03b3f8bca9417920035ed01"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a left handed frustum matrix.  <a href="a00243.html#ga259520cad03b3f8bca9417920035ed01">More...</a><br /></td></tr>
<tr class="separator:ga259520cad03b3f8bca9417920035ed01"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga94218b094862d17798370242680b9030"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga94218b094862d17798370242680b9030"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#ga94218b094862d17798370242680b9030">frustumLH_ZO</a> (T left, T right, T bottom, T top, T near, T far)</td></tr>
<tr class="memdesc:ga94218b094862d17798370242680b9030"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a left handed frustum matrix.  <a href="a00243.html#ga94218b094862d17798370242680b9030">More...</a><br /></td></tr>
<tr class="separator:ga94218b094862d17798370242680b9030"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae34ec664ad44860bf4b5ba631f0e0e90"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gae34ec664ad44860bf4b5ba631f0e0e90"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#gae34ec664ad44860bf4b5ba631f0e0e90">frustumNO</a> (T left, T right, T bottom, T top, T near, T far)</td></tr>
<tr class="memdesc:gae34ec664ad44860bf4b5ba631f0e0e90"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a frustum matrix using left-handed coordinates if GLM_FORCE_LEFT_HANDED if defined or right-handed coordinates otherwise.  <a href="a00243.html#gae34ec664ad44860bf4b5ba631f0e0e90">More...</a><br /></td></tr>
<tr class="separator:gae34ec664ad44860bf4b5ba631f0e0e90"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4366ab45880c6c5f8b3e8c371ca4b136"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga4366ab45880c6c5f8b3e8c371ca4b136"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#ga4366ab45880c6c5f8b3e8c371ca4b136">frustumRH</a> (T left, T right, T bottom, T top, T near, T far)</td></tr>
<tr class="memdesc:ga4366ab45880c6c5f8b3e8c371ca4b136"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a right handed frustum matrix.  <a href="a00243.html#ga4366ab45880c6c5f8b3e8c371ca4b136">More...</a><br /></td></tr>
<tr class="separator:ga4366ab45880c6c5f8b3e8c371ca4b136"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9236c8439f21be186b79c97b588836b9"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga9236c8439f21be186b79c97b588836b9"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#ga9236c8439f21be186b79c97b588836b9">frustumRH_NO</a> (T left, T right, T bottom, T top, T near, T far)</td></tr>
<tr class="memdesc:ga9236c8439f21be186b79c97b588836b9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a right handed frustum matrix.  <a href="a00243.html#ga9236c8439f21be186b79c97b588836b9">More...</a><br /></td></tr>
<tr class="separator:ga9236c8439f21be186b79c97b588836b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7654a9227f14d5382786b9fc0eb5692d"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga7654a9227f14d5382786b9fc0eb5692d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#ga7654a9227f14d5382786b9fc0eb5692d">frustumRH_ZO</a> (T left, T right, T bottom, T top, T near, T far)</td></tr>
<tr class="memdesc:ga7654a9227f14d5382786b9fc0eb5692d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a right handed frustum matrix.  <a href="a00243.html#ga7654a9227f14d5382786b9fc0eb5692d">More...</a><br /></td></tr>
<tr class="separator:ga7654a9227f14d5382786b9fc0eb5692d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa73322e152edf50cf30a6edac342a757"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaa73322e152edf50cf30a6edac342a757"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#gaa73322e152edf50cf30a6edac342a757">frustumZO</a> (T left, T right, T bottom, T top, T near, T far)</td></tr>
<tr class="memdesc:gaa73322e152edf50cf30a6edac342a757"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a frustum matrix using left-handed coordinates if GLM_FORCE_LEFT_HANDED if defined or right-handed coordinates otherwise.  <a href="a00243.html#gaa73322e152edf50cf30a6edac342a757">More...</a><br /></td></tr>
<tr class="separator:gaa73322e152edf50cf30a6edac342a757"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga44fa38a18349450325cae2661bb115ca"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga44fa38a18349450325cae2661bb115ca"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#ga44fa38a18349450325cae2661bb115ca">infinitePerspective</a> (T fovy, T aspect, T near)</td></tr>
<tr class="memdesc:ga44fa38a18349450325cae2661bb115ca"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for a symmetric perspective-view frustum with far plane at infinite with default handedness.  <a href="a00243.html#ga44fa38a18349450325cae2661bb115ca">More...</a><br /></td></tr>
<tr class="separator:ga44fa38a18349450325cae2661bb115ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3201b30f5b3ea0f933246d87bfb992a9"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga3201b30f5b3ea0f933246d87bfb992a9"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#ga3201b30f5b3ea0f933246d87bfb992a9">infinitePerspectiveLH</a> (T fovy, T aspect, T near)</td></tr>
<tr class="memdesc:ga3201b30f5b3ea0f933246d87bfb992a9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for a left handed, symmetric perspective-view frustum with far plane at infinite.  <a href="a00243.html#ga3201b30f5b3ea0f933246d87bfb992a9">More...</a><br /></td></tr>
<tr class="separator:ga3201b30f5b3ea0f933246d87bfb992a9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga99672ffe5714ef478dab2437255fe7e1"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga99672ffe5714ef478dab2437255fe7e1"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#ga99672ffe5714ef478dab2437255fe7e1">infinitePerspectiveRH</a> (T fovy, T aspect, T near)</td></tr>
<tr class="memdesc:ga99672ffe5714ef478dab2437255fe7e1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for a right handed, symmetric perspective-view frustum with far plane at infinite.  <a href="a00243.html#ga99672ffe5714ef478dab2437255fe7e1">More...</a><br /></td></tr>
<tr class="separator:ga99672ffe5714ef478dab2437255fe7e1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae5b6b40ed882cd56cd7cb97701909c06"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gae5b6b40ed882cd56cd7cb97701909c06"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#gae5b6b40ed882cd56cd7cb97701909c06">ortho</a> (T left, T right, T bottom, T top)</td></tr>
<tr class="memdesc:gae5b6b40ed882cd56cd7cb97701909c06"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for projecting two-dimensional coordinates onto the screen.  <a href="a00243.html#gae5b6b40ed882cd56cd7cb97701909c06">More...</a><br /></td></tr>
<tr class="separator:gae5b6b40ed882cd56cd7cb97701909c06"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6615d8a9d39432e279c4575313ecb456"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga6615d8a9d39432e279c4575313ecb456"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#ga6615d8a9d39432e279c4575313ecb456">ortho</a> (T left, T right, T bottom, T top, T zNear, T zFar)</td></tr>
<tr class="memdesc:ga6615d8a9d39432e279c4575313ecb456"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for an orthographic parallel viewing volume, using the default handedness and default near and far clip planes definition.  <a href="a00243.html#ga6615d8a9d39432e279c4575313ecb456">More...</a><br /></td></tr>
<tr class="separator:ga6615d8a9d39432e279c4575313ecb456"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad122a79aadaa5529cec4ac197203db7f"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gad122a79aadaa5529cec4ac197203db7f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#gad122a79aadaa5529cec4ac197203db7f">orthoLH</a> (T left, T right, T bottom, T top, T zNear, T zFar)</td></tr>
<tr class="memdesc:gad122a79aadaa5529cec4ac197203db7f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for an orthographic parallel viewing volume, using left-handed coordinates.  <a href="a00243.html#gad122a79aadaa5529cec4ac197203db7f">More...</a><br /></td></tr>
<tr class="separator:gad122a79aadaa5529cec4ac197203db7f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga526416735ea7c5c5cd255bf99d051bd8"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga526416735ea7c5c5cd255bf99d051bd8"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#ga526416735ea7c5c5cd255bf99d051bd8">orthoLH_NO</a> (T left, T right, T bottom, T top, T zNear, T zFar)</td></tr>
<tr class="memdesc:ga526416735ea7c5c5cd255bf99d051bd8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for an orthographic parallel viewing volume using right-handed coordinates.  <a href="a00243.html#ga526416735ea7c5c5cd255bf99d051bd8">More...</a><br /></td></tr>
<tr class="separator:ga526416735ea7c5c5cd255bf99d051bd8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab37ac3eec8d61f22fceda7775e836afa"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gab37ac3eec8d61f22fceda7775e836afa"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#gab37ac3eec8d61f22fceda7775e836afa">orthoLH_ZO</a> (T left, T right, T bottom, T top, T zNear, T zFar)</td></tr>
<tr class="memdesc:gab37ac3eec8d61f22fceda7775e836afa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for an orthographic parallel viewing volume, using left-handed coordinates.  <a href="a00243.html#gab37ac3eec8d61f22fceda7775e836afa">More...</a><br /></td></tr>
<tr class="separator:gab37ac3eec8d61f22fceda7775e836afa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab219d28a8f178d4517448fcd6395a073"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gab219d28a8f178d4517448fcd6395a073"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#gab219d28a8f178d4517448fcd6395a073">orthoNO</a> (T left, T right, T bottom, T top, T zNear, T zFar)</td></tr>
<tr class="memdesc:gab219d28a8f178d4517448fcd6395a073"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for an orthographic parallel viewing volume, using left-handed coordinates if GLM_FORCE_LEFT_HANDED if defined or right-handed coordinates otherwise.  <a href="a00243.html#gab219d28a8f178d4517448fcd6395a073">More...</a><br /></td></tr>
<tr class="separator:gab219d28a8f178d4517448fcd6395a073"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga16264c9b838edeb9dd1de7a1010a13a4"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga16264c9b838edeb9dd1de7a1010a13a4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#ga16264c9b838edeb9dd1de7a1010a13a4">orthoRH</a> (T left, T right, T bottom, T top, T zNear, T zFar)</td></tr>
<tr class="memdesc:ga16264c9b838edeb9dd1de7a1010a13a4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for an orthographic parallel viewing volume, using right-handed coordinates.  <a href="a00243.html#ga16264c9b838edeb9dd1de7a1010a13a4">More...</a><br /></td></tr>
<tr class="separator:ga16264c9b838edeb9dd1de7a1010a13a4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa2f7a1373170bf0a4a2ddef9b0706780"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaa2f7a1373170bf0a4a2ddef9b0706780"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#gaa2f7a1373170bf0a4a2ddef9b0706780">orthoRH_NO</a> (T left, T right, T bottom, T top, T zNear, T zFar)</td></tr>
<tr class="memdesc:gaa2f7a1373170bf0a4a2ddef9b0706780"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for an orthographic parallel viewing volume, using right-handed coordinates.  <a href="a00243.html#gaa2f7a1373170bf0a4a2ddef9b0706780">More...</a><br /></td></tr>
<tr class="separator:gaa2f7a1373170bf0a4a2ddef9b0706780"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9aea2e515b08fd7dce47b7b6ec34d588"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga9aea2e515b08fd7dce47b7b6ec34d588"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#ga9aea2e515b08fd7dce47b7b6ec34d588">orthoRH_ZO</a> (T left, T right, T bottom, T top, T zNear, T zFar)</td></tr>
<tr class="memdesc:ga9aea2e515b08fd7dce47b7b6ec34d588"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for an orthographic parallel viewing volume, using left-handed coordinates.  <a href="a00243.html#ga9aea2e515b08fd7dce47b7b6ec34d588">More...</a><br /></td></tr>
<tr class="separator:ga9aea2e515b08fd7dce47b7b6ec34d588"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaea11a70817af2c0801c869dea0b7a5bc"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaea11a70817af2c0801c869dea0b7a5bc"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#gaea11a70817af2c0801c869dea0b7a5bc">orthoZO</a> (T left, T right, T bottom, T top, T zNear, T zFar)</td></tr>
<tr class="memdesc:gaea11a70817af2c0801c869dea0b7a5bc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for an orthographic parallel viewing volume, using left-handed coordinates.  <a href="a00243.html#gaea11a70817af2c0801c869dea0b7a5bc">More...</a><br /></td></tr>
<tr class="separator:gaea11a70817af2c0801c869dea0b7a5bc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga747c8cf99458663dd7ad1bb3a2f07787"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga747c8cf99458663dd7ad1bb3a2f07787"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#ga747c8cf99458663dd7ad1bb3a2f07787">perspective</a> (T fovy, T aspect, T near, T far)</td></tr>
<tr class="memdesc:ga747c8cf99458663dd7ad1bb3a2f07787"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for a symetric perspective-view frustum based on the default handedness and default near and far clip planes definition.  <a href="a00243.html#ga747c8cf99458663dd7ad1bb3a2f07787">More...</a><br /></td></tr>
<tr class="separator:ga747c8cf99458663dd7ad1bb3a2f07787"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaebd02240fd36e85ad754f02ddd9a560d"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaebd02240fd36e85ad754f02ddd9a560d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#gaebd02240fd36e85ad754f02ddd9a560d">perspectiveFov</a> (T fov, T width, T height, T near, T far)</td></tr>
<tr class="memdesc:gaebd02240fd36e85ad754f02ddd9a560d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Builds a perspective projection matrix based on a field of view and the default handedness and default near and far clip planes definition.  <a href="a00243.html#gaebd02240fd36e85ad754f02ddd9a560d">More...</a><br /></td></tr>
<tr class="separator:gaebd02240fd36e85ad754f02ddd9a560d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6aebe16c164bd8e52554cbe0304ef4aa"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga6aebe16c164bd8e52554cbe0304ef4aa"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#ga6aebe16c164bd8e52554cbe0304ef4aa">perspectiveFovLH</a> (T fov, T width, T height, T near, T far)</td></tr>
<tr class="memdesc:ga6aebe16c164bd8e52554cbe0304ef4aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Builds a left handed perspective projection matrix based on a field of view.  <a href="a00243.html#ga6aebe16c164bd8e52554cbe0304ef4aa">More...</a><br /></td></tr>
<tr class="separator:ga6aebe16c164bd8e52554cbe0304ef4aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad18a4495b77530317327e8d466488c1a"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gad18a4495b77530317327e8d466488c1a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#gad18a4495b77530317327e8d466488c1a">perspectiveFovLH_NO</a> (T fov, T width, T height, T near, T far)</td></tr>
<tr class="memdesc:gad18a4495b77530317327e8d466488c1a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Builds a perspective projection matrix based on a field of view using left-handed coordinates.  <a href="a00243.html#gad18a4495b77530317327e8d466488c1a">More...</a><br /></td></tr>
<tr class="separator:gad18a4495b77530317327e8d466488c1a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabdd37014f529e25b2fa1b3ba06c10d5c"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gabdd37014f529e25b2fa1b3ba06c10d5c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#gabdd37014f529e25b2fa1b3ba06c10d5c">perspectiveFovLH_ZO</a> (T fov, T width, T height, T near, T far)</td></tr>
<tr class="memdesc:gabdd37014f529e25b2fa1b3ba06c10d5c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Builds a perspective projection matrix based on a field of view using left-handed coordinates.  <a href="a00243.html#gabdd37014f529e25b2fa1b3ba06c10d5c">More...</a><br /></td></tr>
<tr class="separator:gabdd37014f529e25b2fa1b3ba06c10d5c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf30e7bd3b1387a6776433dd5383e6633"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaf30e7bd3b1387a6776433dd5383e6633"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#gaf30e7bd3b1387a6776433dd5383e6633">perspectiveFovNO</a> (T fov, T width, T height, T near, T far)</td></tr>
<tr class="memdesc:gaf30e7bd3b1387a6776433dd5383e6633"><td class="mdescLeft">&#160;</td><td class="mdescRight">Builds a perspective projection matrix based on a field of view using left-handed coordinates if GLM_FORCE_LEFT_HANDED if defined or right-handed coordinates otherwise.  <a href="a00243.html#gaf30e7bd3b1387a6776433dd5383e6633">More...</a><br /></td></tr>
<tr class="separator:gaf30e7bd3b1387a6776433dd5383e6633"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf32bf563f28379c68554a44ee60c6a85"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaf32bf563f28379c68554a44ee60c6a85"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#gaf32bf563f28379c68554a44ee60c6a85">perspectiveFovRH</a> (T fov, T width, T height, T near, T far)</td></tr>
<tr class="memdesc:gaf32bf563f28379c68554a44ee60c6a85"><td class="mdescLeft">&#160;</td><td class="mdescRight">Builds a right handed perspective projection matrix based on a field of view.  <a href="a00243.html#gaf32bf563f28379c68554a44ee60c6a85">More...</a><br /></td></tr>
<tr class="separator:gaf32bf563f28379c68554a44ee60c6a85"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga257b733ff883c9a065801023cf243eb2"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga257b733ff883c9a065801023cf243eb2"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#ga257b733ff883c9a065801023cf243eb2">perspectiveFovRH_NO</a> (T fov, T width, T height, T near, T far)</td></tr>
<tr class="memdesc:ga257b733ff883c9a065801023cf243eb2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Builds a perspective projection matrix based on a field of view using right-handed coordinates.  <a href="a00243.html#ga257b733ff883c9a065801023cf243eb2">More...</a><br /></td></tr>
<tr class="separator:ga257b733ff883c9a065801023cf243eb2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7dcbb25331676f5b0795aced1a905c44"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga7dcbb25331676f5b0795aced1a905c44"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#ga7dcbb25331676f5b0795aced1a905c44">perspectiveFovRH_ZO</a> (T fov, T width, T height, T near, T far)</td></tr>
<tr class="memdesc:ga7dcbb25331676f5b0795aced1a905c44"><td class="mdescLeft">&#160;</td><td class="mdescRight">Builds a perspective projection matrix based on a field of view using right-handed coordinates.  <a href="a00243.html#ga7dcbb25331676f5b0795aced1a905c44">More...</a><br /></td></tr>
<tr class="separator:ga7dcbb25331676f5b0795aced1a905c44"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4bc69fa1d1f95128430aa3d2a712390b"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga4bc69fa1d1f95128430aa3d2a712390b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#ga4bc69fa1d1f95128430aa3d2a712390b">perspectiveFovZO</a> (T fov, T width, T height, T near, T far)</td></tr>
<tr class="memdesc:ga4bc69fa1d1f95128430aa3d2a712390b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Builds a perspective projection matrix based on a field of view using left-handed coordinates if GLM_FORCE_LEFT_HANDED if defined or right-handed coordinates otherwise.  <a href="a00243.html#ga4bc69fa1d1f95128430aa3d2a712390b">More...</a><br /></td></tr>
<tr class="separator:ga4bc69fa1d1f95128430aa3d2a712390b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9bd34951dc7022ac256fcb51d7f6fc2f"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga9bd34951dc7022ac256fcb51d7f6fc2f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#ga9bd34951dc7022ac256fcb51d7f6fc2f">perspectiveLH</a> (T fovy, T aspect, T near, T far)</td></tr>
<tr class="memdesc:ga9bd34951dc7022ac256fcb51d7f6fc2f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for a left handed, symetric perspective-view frustum.  <a href="a00243.html#ga9bd34951dc7022ac256fcb51d7f6fc2f">More...</a><br /></td></tr>
<tr class="separator:ga9bd34951dc7022ac256fcb51d7f6fc2f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaead4d049d1feab463b700b5641aa590e"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaead4d049d1feab463b700b5641aa590e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#gaead4d049d1feab463b700b5641aa590e">perspectiveLH_NO</a> (T fovy, T aspect, T near, T far)</td></tr>
<tr class="memdesc:gaead4d049d1feab463b700b5641aa590e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for a left handed, symetric perspective-view frustum.  <a href="a00243.html#gaead4d049d1feab463b700b5641aa590e">More...</a><br /></td></tr>
<tr class="separator:gaead4d049d1feab463b700b5641aa590e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaca32af88c2719005c02817ad1142986c"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaca32af88c2719005c02817ad1142986c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#gaca32af88c2719005c02817ad1142986c">perspectiveLH_ZO</a> (T fovy, T aspect, T near, T far)</td></tr>
<tr class="memdesc:gaca32af88c2719005c02817ad1142986c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for a left handed, symetric perspective-view frustum.  <a href="a00243.html#gaca32af88c2719005c02817ad1142986c">More...</a><br /></td></tr>
<tr class="separator:gaca32af88c2719005c02817ad1142986c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf497e6bca61e7c87088370b126a93758"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaf497e6bca61e7c87088370b126a93758"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#gaf497e6bca61e7c87088370b126a93758">perspectiveNO</a> (T fovy, T aspect, T near, T far)</td></tr>
<tr class="memdesc:gaf497e6bca61e7c87088370b126a93758"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for a symetric perspective-view frustum using left-handed coordinates if GLM_FORCE_LEFT_HANDED if defined or right-handed coordinates otherwise.  <a href="a00243.html#gaf497e6bca61e7c87088370b126a93758">More...</a><br /></td></tr>
<tr class="separator:gaf497e6bca61e7c87088370b126a93758"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga26b88757fbd90601b80768a7e1ad3aa1"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga26b88757fbd90601b80768a7e1ad3aa1"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#ga26b88757fbd90601b80768a7e1ad3aa1">perspectiveRH</a> (T fovy, T aspect, T near, T far)</td></tr>
<tr class="memdesc:ga26b88757fbd90601b80768a7e1ad3aa1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for a right handed, symetric perspective-view frustum.  <a href="a00243.html#ga26b88757fbd90601b80768a7e1ad3aa1">More...</a><br /></td></tr>
<tr class="separator:ga26b88757fbd90601b80768a7e1ad3aa1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad1526cb2cbe796095284e8f34b01c582"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gad1526cb2cbe796095284e8f34b01c582"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#gad1526cb2cbe796095284e8f34b01c582">perspectiveRH_NO</a> (T fovy, T aspect, T near, T far)</td></tr>
<tr class="memdesc:gad1526cb2cbe796095284e8f34b01c582"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for a right handed, symetric perspective-view frustum.  <a href="a00243.html#gad1526cb2cbe796095284e8f34b01c582">More...</a><br /></td></tr>
<tr class="separator:gad1526cb2cbe796095284e8f34b01c582"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4da358d6e1b8e5b9ae35d1f3f2dc3b9a"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga4da358d6e1b8e5b9ae35d1f3f2dc3b9a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#ga4da358d6e1b8e5b9ae35d1f3f2dc3b9a">perspectiveRH_ZO</a> (T fovy, T aspect, T near, T far)</td></tr>
<tr class="memdesc:ga4da358d6e1b8e5b9ae35d1f3f2dc3b9a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for a right handed, symetric perspective-view frustum.  <a href="a00243.html#ga4da358d6e1b8e5b9ae35d1f3f2dc3b9a">More...</a><br /></td></tr>
<tr class="separator:ga4da358d6e1b8e5b9ae35d1f3f2dc3b9a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa9dfba5c2322da54f72b1eb7c7c11b47"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaa9dfba5c2322da54f72b1eb7c7c11b47"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#gaa9dfba5c2322da54f72b1eb7c7c11b47">perspectiveZO</a> (T fovy, T aspect, T near, T far)</td></tr>
<tr class="memdesc:gaa9dfba5c2322da54f72b1eb7c7c11b47"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for a symetric perspective-view frustum using left-handed coordinates if GLM_FORCE_LEFT_HANDED if defined or right-handed coordinates otherwise.  <a href="a00243.html#gaa9dfba5c2322da54f72b1eb7c7c11b47">More...</a><br /></td></tr>
<tr class="separator:gaa9dfba5c2322da54f72b1eb7c7c11b47"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaeacc04a2a6f4b18c5899d37e7bb3ef9"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaaeacc04a2a6f4b18c5899d37e7bb3ef9"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#gaaeacc04a2a6f4b18c5899d37e7bb3ef9">tweakedInfinitePerspective</a> (T fovy, T aspect, T near)</td></tr>
<tr class="memdesc:gaaeacc04a2a6f4b18c5899d37e7bb3ef9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for a symmetric perspective-view frustum with far plane at infinite for graphics hardware that doesn't support depth clamping.  <a href="a00243.html#gaaeacc04a2a6f4b18c5899d37e7bb3ef9">More...</a><br /></td></tr>
<tr class="separator:gaaeacc04a2a6f4b18c5899d37e7bb3ef9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf5b3c85ff6737030a1d2214474ffa7a8"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaf5b3c85ff6737030a1d2214474ffa7a8"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00243.html#gaf5b3c85ff6737030a1d2214474ffa7a8">tweakedInfinitePerspective</a> (T fovy, T aspect, T near, T ep)</td></tr>
<tr class="memdesc:gaf5b3c85ff6737030a1d2214474ffa7a8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a matrix for a symmetric perspective-view frustum with far plane at infinite for graphics hardware that doesn't support depth clamping.  <a href="a00243.html#gaf5b3c85ff6737030a1d2214474ffa7a8">More...</a><br /></td></tr>
<tr class="separator:gaf5b3c85ff6737030a1d2214474ffa7a8"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Defines functions that generate clip space transformation matrices. </p>
<p>The matrices generated by this extension use standard OpenGL fixed-function conventions. For example, the lookAt function generates a transform from world space into the specific eye space that the projective matrix functions (perspective, ortho, etc) are designed to expect. The OpenGL compatibility specifications defines the particular layout of this eye space.</p>
<p>Include &lt;<a class="el" href="a00059.html" title="GLM_EXT_matrix_clip_space ">glm/ext/matrix_clip_space.hpp</a>&gt; to use the features of this extension.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00247.html" title="Defines functions that generate common transformation matrices. ">GLM_EXT_matrix_transform</a> </dd>
<dd>
<a class="el" href="a00245.html" title="Functions that generate common projection transformation matrices. ">GLM_EXT_matrix_projection</a> </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga0bcd4542e0affc63a0b8c08fcb839ea9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::frustum </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>left</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>right</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>bottom</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>top</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a frustum matrix with default handedness, using the default handedness and default near and far clip planes definition. </p>
<p>To change default handedness use GLM_FORCE_LEFT_HANDED. To change default near and far clip planes definition use GLM_FORCE_DEPTH_ZERO_TO_ONE.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glFrustum.xml">glFrustum man page</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae4277c37f61d81da01bc9db14ea90296"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::frustumLH </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>left</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>right</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>bottom</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>top</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a left handed frustum matrix. </p>
<p>If GLM_FORCE_DEPTH_ZERO_TO_ONE is defined, the near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition) Otherwise, the near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga259520cad03b3f8bca9417920035ed01"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::frustumLH_NO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>left</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>right</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>bottom</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>top</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a left handed frustum matrix. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga94218b094862d17798370242680b9030"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::frustumLH_ZO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>left</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>right</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>bottom</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>top</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a left handed frustum matrix. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition)</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gae34ec664ad44860bf4b5ba631f0e0e90"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::frustumNO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>left</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>right</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>bottom</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>top</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a frustum matrix using left-handed coordinates if GLM_FORCE_LEFT_HANDED if defined or right-handed coordinates otherwise. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga4366ab45880c6c5f8b3e8c371ca4b136"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::frustumRH </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>left</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>right</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>bottom</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>top</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a right handed frustum matrix. </p>
<p>If GLM_FORCE_DEPTH_ZERO_TO_ONE is defined, the near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition) Otherwise, the near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga9236c8439f21be186b79c97b588836b9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::frustumRH_NO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>left</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>right</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>bottom</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>top</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a right handed frustum matrix. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga7654a9227f14d5382786b9fc0eb5692d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::frustumRH_ZO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>left</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>right</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>bottom</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>top</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a right handed frustum matrix. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition)</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gaa73322e152edf50cf30a6edac342a757"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::frustumZO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>left</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>right</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>bottom</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>top</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a frustum matrix using left-handed coordinates if GLM_FORCE_LEFT_HANDED if defined or right-handed coordinates otherwise. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition)</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga44fa38a18349450325cae2661bb115ca"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::infinitePerspective </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fovy</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>aspect</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for a symmetric perspective-view frustum with far plane at infinite with default handedness. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fovy</td><td>Specifies the field of view angle, in degrees, in the y direction. Expressed in radians. </td></tr>
    <tr><td class="paramname">aspect</td><td>Specifies the aspect ratio that determines the field of view in the x direction. The aspect ratio is the ratio of x (width) to y (height). </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga3201b30f5b3ea0f933246d87bfb992a9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::infinitePerspectiveLH </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fovy</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>aspect</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for a left handed, symmetric perspective-view frustum with far plane at infinite. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fovy</td><td>Specifies the field of view angle, in degrees, in the y direction. Expressed in radians. </td></tr>
    <tr><td class="paramname">aspect</td><td>Specifies the aspect ratio that determines the field of view in the x direction. The aspect ratio is the ratio of x (width) to y (height). </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga99672ffe5714ef478dab2437255fe7e1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::infinitePerspectiveRH </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fovy</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>aspect</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for a right handed, symmetric perspective-view frustum with far plane at infinite. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fovy</td><td>Specifies the field of view angle, in degrees, in the y direction. Expressed in radians. </td></tr>
    <tr><td class="paramname">aspect</td><td>Specifies the aspect ratio that determines the field of view in the x direction. The aspect ratio is the ratio of x (width) to y (height). </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gae5b6b40ed882cd56cd7cb97701909c06"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::ortho </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>left</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>right</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>bottom</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>top</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for projecting two-dimensional coordinates onto the screen. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>- glm::ortho(T const&amp; left, T const&amp; right, T const&amp; bottom, T const&amp; top, T const&amp; zNear, T const&amp; zFar) </dd>
<dd>
<a href="https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/gluOrtho2D.xml">gluOrtho2D man page</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga6615d8a9d39432e279c4575313ecb456"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::ortho </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>left</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>right</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>bottom</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>top</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>zNear</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>zFar</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for an orthographic parallel viewing volume, using the default handedness and default near and far clip planes definition. </p>
<p>To change default handedness use GLM_FORCE_LEFT_HANDED. To change default near and far clip planes definition use GLM_FORCE_DEPTH_ZERO_TO_ONE.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>- glm::ortho(T const&amp; left, T const&amp; right, T const&amp; bottom, T const&amp; top) </dd>
<dd>
<a href="https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glOrtho.xml">glOrtho man page</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gad122a79aadaa5529cec4ac197203db7f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::orthoLH </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>left</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>right</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>bottom</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>top</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>zNear</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>zFar</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for an orthographic parallel viewing volume, using left-handed coordinates. </p>
<p>If GLM_FORCE_DEPTH_ZERO_TO_ONE is defined, the near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition) Otherwise, the near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>- glm::ortho(T const&amp; left, T const&amp; right, T const&amp; bottom, T const&amp; top) </dd></dl>

</div>
</div>
<a class="anchor" id="ga526416735ea7c5c5cd255bf99d051bd8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::orthoLH_NO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>left</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>right</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>bottom</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>top</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>zNear</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>zFar</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for an orthographic parallel viewing volume using right-handed coordinates. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>- glm::ortho(T const&amp; left, T const&amp; right, T const&amp; bottom, T const&amp; top) </dd></dl>

</div>
</div>
<a class="anchor" id="gab37ac3eec8d61f22fceda7775e836afa"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::orthoLH_ZO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>left</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>right</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>bottom</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>top</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>zNear</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>zFar</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for an orthographic parallel viewing volume, using left-handed coordinates. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition)</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>- glm::ortho(T const&amp; left, T const&amp; right, T const&amp; bottom, T const&amp; top) </dd></dl>

</div>
</div>
<a class="anchor" id="gab219d28a8f178d4517448fcd6395a073"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::orthoNO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>left</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>right</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>bottom</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>top</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>zNear</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>zFar</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for an orthographic parallel viewing volume, using left-handed coordinates if GLM_FORCE_LEFT_HANDED if defined or right-handed coordinates otherwise. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>- glm::ortho(T const&amp; left, T const&amp; right, T const&amp; bottom, T const&amp; top) </dd></dl>

</div>
</div>
<a class="anchor" id="ga16264c9b838edeb9dd1de7a1010a13a4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::orthoRH </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>left</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>right</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>bottom</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>top</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>zNear</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>zFar</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for an orthographic parallel viewing volume, using right-handed coordinates. </p>
<p>If GLM_FORCE_DEPTH_ZERO_TO_ONE is defined, the near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition) Otherwise, the near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>- glm::ortho(T const&amp; left, T const&amp; right, T const&amp; bottom, T const&amp; top) </dd></dl>

</div>
</div>
<a class="anchor" id="gaa2f7a1373170bf0a4a2ddef9b0706780"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::orthoRH_NO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>left</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>right</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>bottom</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>top</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>zNear</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>zFar</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for an orthographic parallel viewing volume, using right-handed coordinates. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>- glm::ortho(T const&amp; left, T const&amp; right, T const&amp; bottom, T const&amp; top) </dd></dl>

</div>
</div>
<a class="anchor" id="ga9aea2e515b08fd7dce47b7b6ec34d588"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::orthoRH_ZO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>left</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>right</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>bottom</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>top</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>zNear</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>zFar</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for an orthographic parallel viewing volume, using left-handed coordinates. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition)</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>- glm::ortho(T const&amp; left, T const&amp; right, T const&amp; bottom, T const&amp; top) </dd></dl>

</div>
</div>
<a class="anchor" id="gaea11a70817af2c0801c869dea0b7a5bc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::orthoZO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>left</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>right</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>bottom</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>top</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>zNear</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>zFar</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for an orthographic parallel viewing volume, using left-handed coordinates. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition)</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>- glm::ortho(T const&amp; left, T const&amp; right, T const&amp; bottom, T const&amp; top) </dd></dl>

</div>
</div>
<a class="anchor" id="ga747c8cf99458663dd7ad1bb3a2f07787"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::perspective </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fovy</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>aspect</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for a symetric perspective-view frustum based on the default handedness and default near and far clip planes definition. </p>
<p>To change default handedness use GLM_FORCE_LEFT_HANDED. To change default near and far clip planes definition use GLM_FORCE_DEPTH_ZERO_TO_ONE.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fovy</td><td>Specifies the field of view angle in the y direction. Expressed in radians. </td></tr>
    <tr><td class="paramname">aspect</td><td>Specifies the aspect ratio that determines the field of view in the x direction. The aspect ratio is the ratio of x (width) to y (height). </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive). </td></tr>
    <tr><td class="paramname">far</td><td>Specifies the distance from the viewer to the far clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/gluPerspective.xml">gluPerspective man page</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaebd02240fd36e85ad754f02ddd9a560d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::perspectiveFov </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fov</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>width</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>height</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Builds a perspective projection matrix based on a field of view and the default handedness and default near and far clip planes definition. </p>
<p>To change default handedness use GLM_FORCE_LEFT_HANDED. To change default near and far clip planes definition use GLM_FORCE_DEPTH_ZERO_TO_ONE.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fov</td><td>Expressed in radians. </td></tr>
    <tr><td class="paramname">width</td><td>Width of the viewport </td></tr>
    <tr><td class="paramname">height</td><td>Height of the viewport </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive). </td></tr>
    <tr><td class="paramname">far</td><td>Specifies the distance from the viewer to the far clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga6aebe16c164bd8e52554cbe0304ef4aa"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::perspectiveFovLH </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fov</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>width</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>height</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Builds a left handed perspective projection matrix based on a field of view. </p>
<p>If GLM_FORCE_DEPTH_ZERO_TO_ONE is defined, the near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition) Otherwise, the near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fov</td><td>Expressed in radians. </td></tr>
    <tr><td class="paramname">width</td><td>Width of the viewport </td></tr>
    <tr><td class="paramname">height</td><td>Height of the viewport </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive). </td></tr>
    <tr><td class="paramname">far</td><td>Specifies the distance from the viewer to the far clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gad18a4495b77530317327e8d466488c1a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::perspectiveFovLH_NO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fov</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>width</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>height</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Builds a perspective projection matrix based on a field of view using left-handed coordinates. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fov</td><td>Expressed in radians. </td></tr>
    <tr><td class="paramname">width</td><td>Width of the viewport </td></tr>
    <tr><td class="paramname">height</td><td>Height of the viewport </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive). </td></tr>
    <tr><td class="paramname">far</td><td>Specifies the distance from the viewer to the far clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gabdd37014f529e25b2fa1b3ba06c10d5c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::perspectiveFovLH_ZO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fov</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>width</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>height</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Builds a perspective projection matrix based on a field of view using left-handed coordinates. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fov</td><td>Expressed in radians. </td></tr>
    <tr><td class="paramname">width</td><td>Width of the viewport </td></tr>
    <tr><td class="paramname">height</td><td>Height of the viewport </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive). </td></tr>
    <tr><td class="paramname">far</td><td>Specifies the distance from the viewer to the far clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gaf30e7bd3b1387a6776433dd5383e6633"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::perspectiveFovNO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fov</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>width</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>height</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Builds a perspective projection matrix based on a field of view using left-handed coordinates if GLM_FORCE_LEFT_HANDED if defined or right-handed coordinates otherwise. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fov</td><td>Expressed in radians. </td></tr>
    <tr><td class="paramname">width</td><td>Width of the viewport </td></tr>
    <tr><td class="paramname">height</td><td>Height of the viewport </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive). </td></tr>
    <tr><td class="paramname">far</td><td>Specifies the distance from the viewer to the far clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gaf32bf563f28379c68554a44ee60c6a85"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::perspectiveFovRH </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fov</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>width</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>height</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Builds a right handed perspective projection matrix based on a field of view. </p>
<p>If GLM_FORCE_DEPTH_ZERO_TO_ONE is defined, the near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition) Otherwise, the near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fov</td><td>Expressed in radians. </td></tr>
    <tr><td class="paramname">width</td><td>Width of the viewport </td></tr>
    <tr><td class="paramname">height</td><td>Height of the viewport </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive). </td></tr>
    <tr><td class="paramname">far</td><td>Specifies the distance from the viewer to the far clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga257b733ff883c9a065801023cf243eb2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::perspectiveFovRH_NO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fov</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>width</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>height</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Builds a perspective projection matrix based on a field of view using right-handed coordinates. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fov</td><td>Expressed in radians. </td></tr>
    <tr><td class="paramname">width</td><td>Width of the viewport </td></tr>
    <tr><td class="paramname">height</td><td>Height of the viewport </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive). </td></tr>
    <tr><td class="paramname">far</td><td>Specifies the distance from the viewer to the far clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga7dcbb25331676f5b0795aced1a905c44"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::perspectiveFovRH_ZO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fov</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>width</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>height</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Builds a perspective projection matrix based on a field of view using right-handed coordinates. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fov</td><td>Expressed in radians. </td></tr>
    <tr><td class="paramname">width</td><td>Width of the viewport </td></tr>
    <tr><td class="paramname">height</td><td>Height of the viewport </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive). </td></tr>
    <tr><td class="paramname">far</td><td>Specifies the distance from the viewer to the far clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga4bc69fa1d1f95128430aa3d2a712390b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::perspectiveFovZO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fov</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>width</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>height</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Builds a perspective projection matrix based on a field of view using left-handed coordinates if GLM_FORCE_LEFT_HANDED if defined or right-handed coordinates otherwise. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fov</td><td>Expressed in radians. </td></tr>
    <tr><td class="paramname">width</td><td>Width of the viewport </td></tr>
    <tr><td class="paramname">height</td><td>Height of the viewport </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive). </td></tr>
    <tr><td class="paramname">far</td><td>Specifies the distance from the viewer to the far clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga9bd34951dc7022ac256fcb51d7f6fc2f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::perspectiveLH </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fovy</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>aspect</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for a left handed, symetric perspective-view frustum. </p>
<p>If GLM_FORCE_DEPTH_ZERO_TO_ONE is defined, the near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition) Otherwise, the near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fovy</td><td>Specifies the field of view angle, in degrees, in the y direction. Expressed in radians. </td></tr>
    <tr><td class="paramname">aspect</td><td>Specifies the aspect ratio that determines the field of view in the x direction. The aspect ratio is the ratio of x (width) to y (height). </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive). </td></tr>
    <tr><td class="paramname">far</td><td>Specifies the distance from the viewer to the far clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gaead4d049d1feab463b700b5641aa590e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::perspectiveLH_NO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fovy</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>aspect</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for a left handed, symetric perspective-view frustum. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fovy</td><td>Specifies the field of view angle, in degrees, in the y direction. Expressed in radians. </td></tr>
    <tr><td class="paramname">aspect</td><td>Specifies the aspect ratio that determines the field of view in the x direction. The aspect ratio is the ratio of x (width) to y (height). </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive). </td></tr>
    <tr><td class="paramname">far</td><td>Specifies the distance from the viewer to the far clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gaca32af88c2719005c02817ad1142986c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::perspectiveLH_ZO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fovy</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>aspect</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for a left handed, symetric perspective-view frustum. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fovy</td><td>Specifies the field of view angle, in degrees, in the y direction. Expressed in radians. </td></tr>
    <tr><td class="paramname">aspect</td><td>Specifies the aspect ratio that determines the field of view in the x direction. The aspect ratio is the ratio of x (width) to y (height). </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive). </td></tr>
    <tr><td class="paramname">far</td><td>Specifies the distance from the viewer to the far clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gaf497e6bca61e7c87088370b126a93758"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::perspectiveNO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fovy</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>aspect</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for a symetric perspective-view frustum using left-handed coordinates if GLM_FORCE_LEFT_HANDED if defined or right-handed coordinates otherwise. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fovy</td><td>Specifies the field of view angle, in degrees, in the y direction. Expressed in radians. </td></tr>
    <tr><td class="paramname">aspect</td><td>Specifies the aspect ratio that determines the field of view in the x direction. The aspect ratio is the ratio of x (width) to y (height). </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive). </td></tr>
    <tr><td class="paramname">far</td><td>Specifies the distance from the viewer to the far clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga26b88757fbd90601b80768a7e1ad3aa1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::perspectiveRH </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fovy</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>aspect</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for a right handed, symetric perspective-view frustum. </p>
<p>If GLM_FORCE_DEPTH_ZERO_TO_ONE is defined, the near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition) Otherwise, the near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fovy</td><td>Specifies the field of view angle, in degrees, in the y direction. Expressed in radians. </td></tr>
    <tr><td class="paramname">aspect</td><td>Specifies the aspect ratio that determines the field of view in the x direction. The aspect ratio is the ratio of x (width) to y (height). </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive). </td></tr>
    <tr><td class="paramname">far</td><td>Specifies the distance from the viewer to the far clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gad1526cb2cbe796095284e8f34b01c582"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::perspectiveRH_NO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fovy</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>aspect</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for a right handed, symetric perspective-view frustum. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fovy</td><td>Specifies the field of view angle, in degrees, in the y direction. Expressed in radians. </td></tr>
    <tr><td class="paramname">aspect</td><td>Specifies the aspect ratio that determines the field of view in the x direction. The aspect ratio is the ratio of x (width) to y (height). </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive). </td></tr>
    <tr><td class="paramname">far</td><td>Specifies the distance from the viewer to the far clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga4da358d6e1b8e5b9ae35d1f3f2dc3b9a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::perspectiveRH_ZO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fovy</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>aspect</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for a right handed, symetric perspective-view frustum. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fovy</td><td>Specifies the field of view angle, in degrees, in the y direction. Expressed in radians. </td></tr>
    <tr><td class="paramname">aspect</td><td>Specifies the aspect ratio that determines the field of view in the x direction. The aspect ratio is the ratio of x (width) to y (height). </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive). </td></tr>
    <tr><td class="paramname">far</td><td>Specifies the distance from the viewer to the far clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gaa9dfba5c2322da54f72b1eb7c7c11b47"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::perspectiveZO </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fovy</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>aspect</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>far</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for a symetric perspective-view frustum using left-handed coordinates if GLM_FORCE_LEFT_HANDED if defined or right-handed coordinates otherwise. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fovy</td><td>Specifies the field of view angle, in degrees, in the y direction. Expressed in radians. </td></tr>
    <tr><td class="paramname">aspect</td><td>Specifies the aspect ratio that determines the field of view in the x direction. The aspect ratio is the ratio of x (width) to y (height). </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive). </td></tr>
    <tr><td class="paramname">far</td><td>Specifies the distance from the viewer to the far clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gaaeacc04a2a6f4b18c5899d37e7bb3ef9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::tweakedInfinitePerspective </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fovy</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>aspect</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for a symmetric perspective-view frustum with far plane at infinite for graphics hardware that doesn't support depth clamping. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fovy</td><td>Specifies the field of view angle, in degrees, in the y direction. Expressed in radians. </td></tr>
    <tr><td class="paramname">aspect</td><td>Specifies the aspect ratio that determines the field of view in the x direction. The aspect ratio is the ratio of x (width) to y (height). </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive).</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gaf5b3c85ff6737030a1d2214474ffa7a8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::tweakedInfinitePerspective </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>fovy</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>aspect</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>near</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>ep</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a matrix for a symmetric perspective-view frustum with far plane at infinite for graphics hardware that doesn't support depth clamping. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">fovy</td><td>Specifies the field of view angle, in degrees, in the y direction. Expressed in radians. </td></tr>
    <tr><td class="paramname">aspect</td><td>Specifies the aspect ratio that determines the field of view in the x direction. The aspect ratio is the ratio of x (width) to y (height). </td></tr>
    <tr><td class="paramname">near</td><td>Specifies the distance from the viewer to the near clipping plane (always positive). </td></tr>
    <tr><td class="paramname">ep</td><td>Epsilon</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
