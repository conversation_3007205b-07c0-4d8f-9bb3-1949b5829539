<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTC_random</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTC_random<div class="ingroups"><a class="el" href="a00286.html">Recommended extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00137.html" title="GLM_GTC_random ">glm/gtc/random.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga7c53b7797f3147af68a11c767679fa3f"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga7c53b7797f3147af68a11c767679fa3f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00300.html#ga7c53b7797f3147af68a11c767679fa3f">ballRand</a> (T Radius)</td></tr>
<tr class="memdesc:ga7c53b7797f3147af68a11c767679fa3f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Generate a random 3D vector which coordinates are regulary distributed within the volume of a ball of a given radius.  <a href="a00300.html#ga7c53b7797f3147af68a11c767679fa3f">More...</a><br /></td></tr>
<tr class="separator:ga7c53b7797f3147af68a11c767679fa3f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9dd05c36025088fae25b97c869e88517"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga9dd05c36025088fae25b97c869e88517"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 2, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00300.html#ga9dd05c36025088fae25b97c869e88517">circularRand</a> (T Radius)</td></tr>
<tr class="memdesc:ga9dd05c36025088fae25b97c869e88517"><td class="mdescLeft">&#160;</td><td class="mdescRight">Generate a random 2D vector which coordinates are regulary distributed on a circle of a given radius.  <a href="a00300.html#ga9dd05c36025088fae25b97c869e88517">More...</a><br /></td></tr>
<tr class="separator:ga9dd05c36025088fae25b97c869e88517"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa0b18071f3f97dbf8bcf6f53c6fe5f73"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaa0b18071f3f97dbf8bcf6f53c6fe5f73"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 2, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00300.html#gaa0b18071f3f97dbf8bcf6f53c6fe5f73">diskRand</a> (T Radius)</td></tr>
<tr class="memdesc:gaa0b18071f3f97dbf8bcf6f53c6fe5f73"><td class="mdescLeft">&#160;</td><td class="mdescRight">Generate a random 2D vector which coordinates are regulary distributed within the area of a disk of a given radius.  <a href="a00300.html#gaa0b18071f3f97dbf8bcf6f53c6fe5f73">More...</a><br /></td></tr>
<tr class="separator:gaa0b18071f3f97dbf8bcf6f53c6fe5f73"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5193a83e49e4fdc5652c084711083574"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga5193a83e49e4fdc5652c084711083574"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00300.html#ga5193a83e49e4fdc5652c084711083574">gaussRand</a> (genType Mean, genType Deviation)</td></tr>
<tr class="memdesc:ga5193a83e49e4fdc5652c084711083574"><td class="mdescLeft">&#160;</td><td class="mdescRight">Generate random numbers in the interval [Min, Max], according a gaussian distribution.  <a href="a00300.html#ga5193a83e49e4fdc5652c084711083574">More...</a><br /></td></tr>
<tr class="separator:ga5193a83e49e4fdc5652c084711083574"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga04e241ab88374a477a2c2ceadd2fa03d"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga04e241ab88374a477a2c2ceadd2fa03d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00300.html#ga04e241ab88374a477a2c2ceadd2fa03d">linearRand</a> (genType Min, genType Max)</td></tr>
<tr class="memdesc:ga04e241ab88374a477a2c2ceadd2fa03d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Generate random numbers in the interval [Min, Max], according a linear distribution.  <a href="a00300.html#ga04e241ab88374a477a2c2ceadd2fa03d">More...</a><br /></td></tr>
<tr class="separator:ga04e241ab88374a477a2c2ceadd2fa03d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga94731130c298a9ff5e5025fdee6d97a0"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga94731130c298a9ff5e5025fdee6d97a0"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00300.html#ga94731130c298a9ff5e5025fdee6d97a0">linearRand</a> (vec&lt; L, T, Q &gt; const &amp;Min, vec&lt; L, T, Q &gt; const &amp;Max)</td></tr>
<tr class="memdesc:ga94731130c298a9ff5e5025fdee6d97a0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Generate random numbers in the interval [Min, Max], according a linear distribution.  <a href="a00300.html#ga94731130c298a9ff5e5025fdee6d97a0">More...</a><br /></td></tr>
<tr class="separator:ga94731130c298a9ff5e5025fdee6d97a0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga22f90fcaccdf001c516ca90f6428e138"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga22f90fcaccdf001c516ca90f6428e138"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00300.html#ga22f90fcaccdf001c516ca90f6428e138">sphericalRand</a> (T Radius)</td></tr>
<tr class="memdesc:ga22f90fcaccdf001c516ca90f6428e138"><td class="mdescLeft">&#160;</td><td class="mdescRight">Generate a random 3D vector which coordinates are regulary distributed on a sphere of a given radius.  <a href="a00300.html#ga22f90fcaccdf001c516ca90f6428e138">More...</a><br /></td></tr>
<tr class="separator:ga22f90fcaccdf001c516ca90f6428e138"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00137.html" title="GLM_GTC_random ">glm/gtc/random.hpp</a>&gt; to use the features of this extension. </p>
<p>Generate random number from various distribution methods. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga7c53b7797f3147af68a11c767679fa3f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, defaultp&gt; glm::ballRand </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>Radius</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Generate a random 3D vector which coordinates are regulary distributed within the volume of a ball of a given radius. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00300.html" title="Include <glm/gtc/random.hpp> to use the features of this extension. ">GLM_GTC_random</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9dd05c36025088fae25b97c869e88517"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;2, T, defaultp&gt; glm::circularRand </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>Radius</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Generate a random 2D vector which coordinates are regulary distributed on a circle of a given radius. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00300.html" title="Include <glm/gtc/random.hpp> to use the features of this extension. ">GLM_GTC_random</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaa0b18071f3f97dbf8bcf6f53c6fe5f73"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;2, T, defaultp&gt; glm::diskRand </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>Radius</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Generate a random 2D vector which coordinates are regulary distributed within the area of a disk of a given radius. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00300.html" title="Include <glm/gtc/random.hpp> to use the features of this extension. ">GLM_GTC_random</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5193a83e49e4fdc5652c084711083574"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::gaussRand </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>Mean</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>Deviation</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Generate random numbers in the interval [Min, Max], according a gaussian distribution. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00300.html" title="Include <glm/gtc/random.hpp> to use the features of this extension. ">GLM_GTC_random</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga04e241ab88374a477a2c2ceadd2fa03d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::linearRand </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>Min</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>Max</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Generate random numbers in the interval [Min, Max], according a linear distribution. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">Min</td><td>Minimum value included in the sampling </td></tr>
    <tr><td class="paramname">Max</td><td>Maximum value included in the sampling </td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Value type. Currently supported: float or double scalars. </td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00300.html" title="Include <glm/gtc/random.hpp> to use the features of this extension. ">GLM_GTC_random</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga94731130c298a9ff5e5025fdee6d97a0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::linearRand </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Min</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Max</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Generate random numbers in the interval [Min, Max], according a linear distribution. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">Min</td><td>Minimum value included in the sampling </td></tr>
    <tr><td class="paramname">Max</td><td>Maximum value included in the sampling </td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Value type. Currently supported: float or double.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00300.html" title="Include <glm/gtc/random.hpp> to use the features of this extension. ">GLM_GTC_random</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga22f90fcaccdf001c516ca90f6428e138"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, defaultp&gt; glm::sphericalRand </td>
          <td>(</td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>Radius</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Generate a random 3D vector which coordinates are regulary distributed on a sphere of a given radius. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00300.html" title="Include <glm/gtc/random.hpp> to use the features of this extension. ">GLM_GTC_random</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
