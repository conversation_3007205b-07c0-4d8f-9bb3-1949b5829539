<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: Floating-Point Pack and Unpack Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Floating-Point Pack and Unpack Functions<div class="ingroups"><a class="el" href="a00280.html">Core features</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Provides GLSL functions to pack and unpack half, single and double-precision floating point values into more compact integer types.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaa916ca426b2bb0343ba17e3753e245c2"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL double&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00372.html#gaa916ca426b2bb0343ba17e3753e245c2">packDouble2x32</a> (uvec2 const &amp;v)</td></tr>
<tr class="memdesc:gaa916ca426b2bb0343ba17e3753e245c2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a double-qualifier value obtained by packing the components of v into a 64-bit value.  <a href="a00372.html#gaa916ca426b2bb0343ba17e3753e245c2">More...</a><br /></td></tr>
<tr class="separator:gaa916ca426b2bb0343ba17e3753e245c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga20f134b07db3a3d3a38efb2617388c92"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00372.html#ga20f134b07db3a3d3a38efb2617388c92">packHalf2x16</a> (vec2 const &amp;v)</td></tr>
<tr class="memdesc:ga20f134b07db3a3d3a38efb2617388c92"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns an unsigned integer obtained by converting the components of a two-component floating-point vector to the 16-bit floating-point representation found in the OpenGL Specification, and then packing these two 16- bit integers into a 32-bit unsigned integer.  <a href="a00372.html#ga20f134b07db3a3d3a38efb2617388c92">More...</a><br /></td></tr>
<tr class="separator:ga20f134b07db3a3d3a38efb2617388c92"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga977ab172da5494e5ac63e952afacfbe2"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00372.html#ga977ab172da5494e5ac63e952afacfbe2">packSnorm2x16</a> (vec2 const &amp;v)</td></tr>
<tr class="memdesc:ga977ab172da5494e5ac63e952afacfbe2"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts each component of the normalized floating-point value v into 8- or 16-bit integer values.  <a href="a00372.html#ga977ab172da5494e5ac63e952afacfbe2">More...</a><br /></td></tr>
<tr class="separator:ga977ab172da5494e5ac63e952afacfbe2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga85e8f17627516445026ab7a9c2e3531a"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00372.html#ga85e8f17627516445026ab7a9c2e3531a">packSnorm4x8</a> (vec4 const &amp;v)</td></tr>
<tr class="memdesc:ga85e8f17627516445026ab7a9c2e3531a"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts each component of the normalized floating-point value v into 8- or 16-bit integer values.  <a href="a00372.html#ga85e8f17627516445026ab7a9c2e3531a">More...</a><br /></td></tr>
<tr class="separator:ga85e8f17627516445026ab7a9c2e3531a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0e2d107039fe608a209497af867b85fb"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00372.html#ga0e2d107039fe608a209497af867b85fb">packUnorm2x16</a> (vec2 const &amp;v)</td></tr>
<tr class="memdesc:ga0e2d107039fe608a209497af867b85fb"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts each component of the normalized floating-point value v into 8- or 16-bit integer values.  <a href="a00372.html#ga0e2d107039fe608a209497af867b85fb">More...</a><br /></td></tr>
<tr class="separator:ga0e2d107039fe608a209497af867b85fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf7d2f7341a9eeb4a436929d6f9ad08f2"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00372.html#gaf7d2f7341a9eeb4a436929d6f9ad08f2">packUnorm4x8</a> (vec4 const &amp;v)</td></tr>
<tr class="memdesc:gaf7d2f7341a9eeb4a436929d6f9ad08f2"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, converts each component of the normalized floating-point value v into 8- or 16-bit integer values.  <a href="a00372.html#gaf7d2f7341a9eeb4a436929d6f9ad08f2">More...</a><br /></td></tr>
<tr class="separator:gaf7d2f7341a9eeb4a436929d6f9ad08f2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5f4296dc5f12f0aa67ac05b8bb322483"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uvec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00372.html#ga5f4296dc5f12f0aa67ac05b8bb322483">unpackDouble2x32</a> (double v)</td></tr>
<tr class="memdesc:ga5f4296dc5f12f0aa67ac05b8bb322483"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a two-component unsigned integer vector representation of v.  <a href="a00372.html#ga5f4296dc5f12f0aa67ac05b8bb322483">More...</a><br /></td></tr>
<tr class="separator:ga5f4296dc5f12f0aa67ac05b8bb322483"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf59b52e6b28da9335322c4ae19b5d745"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00372.html#gaf59b52e6b28da9335322c4ae19b5d745">unpackHalf2x16</a> (uint v)</td></tr>
<tr class="memdesc:gaf59b52e6b28da9335322c4ae19b5d745"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a two-component floating-point vector with components obtained by unpacking a 32-bit unsigned integer into a pair of 16-bit values, interpreting those values as 16-bit floating-point numbers according to the OpenGL Specification, and converting them to 32-bit floating-point values.  <a href="a00372.html#gaf59b52e6b28da9335322c4ae19b5d745">More...</a><br /></td></tr>
<tr class="separator:gaf59b52e6b28da9335322c4ae19b5d745"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacd8f8971a3fe28418be0d0fa1f786b38"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00372.html#gacd8f8971a3fe28418be0d0fa1f786b38">unpackSnorm2x16</a> (uint p)</td></tr>
<tr class="memdesc:gacd8f8971a3fe28418be0d0fa1f786b38"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 32-bit unsigned integer p into a pair of 16-bit unsigned integers, four 8-bit unsigned integers, or four 8-bit signed integers.  <a href="a00372.html#gacd8f8971a3fe28418be0d0fa1f786b38">More...</a><br /></td></tr>
<tr class="separator:gacd8f8971a3fe28418be0d0fa1f786b38"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2db488646d48b7c43d3218954523fe82"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00372.html#ga2db488646d48b7c43d3218954523fe82">unpackSnorm4x8</a> (uint p)</td></tr>
<tr class="memdesc:ga2db488646d48b7c43d3218954523fe82"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 32-bit unsigned integer p into a pair of 16-bit unsigned integers, four 8-bit unsigned integers, or four 8-bit signed integers.  <a href="a00372.html#ga2db488646d48b7c43d3218954523fe82">More...</a><br /></td></tr>
<tr class="separator:ga2db488646d48b7c43d3218954523fe82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1f66188e5d65afeb9ffba1ad971e4007"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00372.html#ga1f66188e5d65afeb9ffba1ad971e4007">unpackUnorm2x16</a> (uint p)</td></tr>
<tr class="memdesc:ga1f66188e5d65afeb9ffba1ad971e4007"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 32-bit unsigned integer p into a pair of 16-bit unsigned integers, four 8-bit unsigned integers, or four 8-bit signed integers.  <a href="a00372.html#ga1f66188e5d65afeb9ffba1ad971e4007">More...</a><br /></td></tr>
<tr class="separator:ga1f66188e5d65afeb9ffba1ad971e4007"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7f903259150b67e9466f5f8edffcd197"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL vec4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00372.html#ga7f903259150b67e9466f5f8edffcd197">unpackUnorm4x8</a> (uint p)</td></tr>
<tr class="memdesc:ga7f903259150b67e9466f5f8edffcd197"><td class="mdescLeft">&#160;</td><td class="mdescRight">First, unpacks a single 32-bit unsigned integer p into a pair of 16-bit unsigned integers, four 8-bit unsigned integers, or four 8-bit signed integers.  <a href="a00372.html#ga7f903259150b67e9466f5f8edffcd197">More...</a><br /></td></tr>
<tr class="separator:ga7f903259150b67e9466f5f8edffcd197"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Provides GLSL functions to pack and unpack half, single and double-precision floating point values into more compact integer types. </p>
<p>These functions do not operate component-wise, rather as described in each case.</p>
<p>Include &lt;<a class="el" href="a00120.html" title="Core features ">glm/packing.hpp</a>&gt; to use these core features. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gaa916ca426b2bb0343ba17e3753e245c2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL double glm::packDouble2x32 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#ga2f6d9ec3ae14813ade37d6aee3715fdb">uvec2</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns a double-qualifier value obtained by packing the components of v into a 64-bit value. </p>
<p>If an IEEE 754 Inf or NaN is created, it will not signal, and the resulting floating point value is unspecified. Otherwise, the bit- level representation of v is preserved. The first vector component specifies the 32 least significant bits; the second component specifies the 32 most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/packDouble2x32.xml">GLSL packDouble2x32 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga20f134b07db3a3d3a38efb2617388c92"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint glm::packHalf2x16 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns an unsigned integer obtained by converting the components of a two-component floating-point vector to the 16-bit floating-point representation found in the OpenGL Specification, and then packing these two 16- bit integers into a 32-bit unsigned integer. </p>
<p>The first vector component specifies the 16 least-significant bits of the result; the second component specifies the 16 most-significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/packHalf2x16.xml">GLSL packHalf2x16 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga977ab172da5494e5ac63e952afacfbe2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint glm::packSnorm2x16 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, converts each component of the normalized floating-point value v into 8- or 16-bit integer values. </p>
<p>Then, the results are packed into the returned 32-bit unsigned integer.</p>
<p>The conversion for component c of v to fixed point is done as follows: packSnorm2x16: round(clamp(v, -1, +1) * 32767.0)</p>
<p>The first component of the vector will be written to the least significant bits of the output; the last component will be written to the most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/packSnorm2x16.xml">GLSL packSnorm2x16 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga85e8f17627516445026ab7a9c2e3531a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint glm::packSnorm4x8 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, converts each component of the normalized floating-point value v into 8- or 16-bit integer values. </p>
<p>Then, the results are packed into the returned 32-bit unsigned integer.</p>
<p>The conversion for component c of v to fixed point is done as follows: packSnorm4x8: round(clamp(c, -1, +1) * 127.0)</p>
<p>The first component of the vector will be written to the least significant bits of the output; the last component will be written to the most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/packSnorm4x8.xml">GLSL packSnorm4x8 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga0e2d107039fe608a209497af867b85fb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint glm::packUnorm2x16 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#gabe65c061834f61b4f7cb6037b19006a4">vec2</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, converts each component of the normalized floating-point value v into 8- or 16-bit integer values. </p>
<p>Then, the results are packed into the returned 32-bit unsigned integer.</p>
<p>The conversion for component c of v to fixed point is done as follows: packUnorm2x16: round(clamp(c, 0, +1) * 65535.0)</p>
<p>The first component of the vector will be written to the least significant bits of the output; the last component will be written to the most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/packUnorm2x16.xml">GLSL packUnorm2x16 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaf7d2f7341a9eeb4a436929d6f9ad08f2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint glm::packUnorm4x8 </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00281.html#gac215a35481a6597d1bf622a382e9d6e2">vec4</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, converts each component of the normalized floating-point value v into 8- or 16-bit integer values. </p>
<p>Then, the results are packed into the returned 32-bit unsigned integer.</p>
<p>The conversion for component c of v to fixed point is done as follows: packUnorm4x8: round(clamp(c, 0, +1) * 255.0)</p>
<p>The first component of the vector will be written to the least significant bits of the output; the last component will be written to the most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/packUnorm4x8.xml">GLSL packUnorm4x8 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5f4296dc5f12f0aa67ac05b8bb322483"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uvec2 glm::unpackDouble2x32 </td>
          <td>(</td>
          <td class="paramtype">double&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns a two-component unsigned integer vector representation of v. </p>
<p>The bit-level representation of v is preserved. The first component of the vector contains the 32 least significant bits of the double; the second component consists the 32 most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/unpackDouble2x32.xml">GLSL unpackDouble2x32 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaf59b52e6b28da9335322c4ae19b5d745"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec2 glm::unpackHalf2x16 </td>
          <td>(</td>
          <td class="paramtype">uint&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns a two-component floating-point vector with components obtained by unpacking a 32-bit unsigned integer into a pair of 16-bit values, interpreting those values as 16-bit floating-point numbers according to the OpenGL Specification, and converting them to 32-bit floating-point values. </p>
<p>The first component of the vector is obtained from the 16 least-significant bits of v; the second component is obtained from the 16 most-significant bits of v.</p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/unpackHalf2x16.xml">GLSL unpackHalf2x16 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gacd8f8971a3fe28418be0d0fa1f786b38"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec2 glm::unpackSnorm2x16 </td>
          <td>(</td>
          <td class="paramtype">uint&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, unpacks a single 32-bit unsigned integer p into a pair of 16-bit unsigned integers, four 8-bit unsigned integers, or four 8-bit signed integers. </p>
<p>Then, each component is converted to a normalized floating-point value to generate the returned two- or four-component vector.</p>
<p>The conversion for unpacked fixed-point value f to floating point is done as follows: unpackSnorm2x16: clamp(f / 32767.0, -1, +1)</p>
<p>The first component of the returned vector will be extracted from the least significant bits of the input; the last component will be extracted from the most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/unpackSnorm2x16.xml">GLSL unpackSnorm2x16 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2db488646d48b7c43d3218954523fe82"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec4 glm::unpackSnorm4x8 </td>
          <td>(</td>
          <td class="paramtype">uint&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, unpacks a single 32-bit unsigned integer p into a pair of 16-bit unsigned integers, four 8-bit unsigned integers, or four 8-bit signed integers. </p>
<p>Then, each component is converted to a normalized floating-point value to generate the returned two- or four-component vector.</p>
<p>The conversion for unpacked fixed-point value f to floating point is done as follows: unpackSnorm4x8: clamp(f / 127.0, -1, +1)</p>
<p>The first component of the returned vector will be extracted from the least significant bits of the input; the last component will be extracted from the most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/unpackSnorm4x8.xml">GLSL unpackSnorm4x8 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga1f66188e5d65afeb9ffba1ad971e4007"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec2 glm::unpackUnorm2x16 </td>
          <td>(</td>
          <td class="paramtype">uint&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, unpacks a single 32-bit unsigned integer p into a pair of 16-bit unsigned integers, four 8-bit unsigned integers, or four 8-bit signed integers. </p>
<p>Then, each component is converted to a normalized floating-point value to generate the returned two- or four-component vector.</p>
<p>The conversion for unpacked fixed-point value f to floating point is done as follows: unpackUnorm2x16: f / 65535.0</p>
<p>The first component of the returned vector will be extracted from the least significant bits of the input; the last component will be extracted from the most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/unpackUnorm2x16.xml">GLSL unpackUnorm2x16 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7f903259150b67e9466f5f8edffcd197"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec4 glm::unpackUnorm4x8 </td>
          <td>(</td>
          <td class="paramtype">uint&#160;</td>
          <td class="paramname"><em>p</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>First, unpacks a single 32-bit unsigned integer p into a pair of 16-bit unsigned integers, four 8-bit unsigned integers, or four 8-bit signed integers. </p>
<p>Then, each component is converted to a normalized floating-point value to generate the returned two- or four-component vector.</p>
<p>The conversion for unpacked fixed-point value f to floating point is done as follows: unpackUnorm4x8: f / 255.0</p>
<p>The first component of the returned vector will be extracted from the least significant bits of the input; the last component will be extracted from the most significant bits.</p>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/unpackUnorm4x8.xml">GLSL unpackUnorm4x8 man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.4 Floating-Point Pack and Unpack Functions</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
