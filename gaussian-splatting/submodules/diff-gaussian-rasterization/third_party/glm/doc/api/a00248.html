<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_EXT_quaternion_common</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_EXT_quaternion_common<div class="ingroups"><a class="el" href="a00285.html">Stable extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Provides common functions for quaternion types.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga10d7bda73201788ac2ab28cd8d0d409b"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga10d7bda73201788ac2ab28cd8d0d409b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00248.html#ga10d7bda73201788ac2ab28cd8d0d409b">conjugate</a> (qua&lt; T, Q &gt; const &amp;q)</td></tr>
<tr class="memdesc:ga10d7bda73201788ac2ab28cd8d0d409b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the q conjugate.  <a href="a00248.html#ga10d7bda73201788ac2ab28cd8d0d409b">More...</a><br /></td></tr>
<tr class="separator:ga10d7bda73201788ac2ab28cd8d0d409b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab41da854ae678e23e114b598cbca4065"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gab41da854ae678e23e114b598cbca4065"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00248.html#gab41da854ae678e23e114b598cbca4065">inverse</a> (qua&lt; T, Q &gt; const &amp;q)</td></tr>
<tr class="memdesc:gab41da854ae678e23e114b598cbca4065"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the q inverse.  <a href="a00248.html#gab41da854ae678e23e114b598cbca4065">More...</a><br /></td></tr>
<tr class="separator:gab41da854ae678e23e114b598cbca4065"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga45722741ea266b4e861938b365c5f362"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga45722741ea266b4e861938b365c5f362"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00248.html#ga45722741ea266b4e861938b365c5f362">isinf</a> (qua&lt; T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga45722741ea266b4e861938b365c5f362"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if x holds a positive infinity or negative infinity representation in the underlying implementation's set of floating point representations.  <a href="a00248.html#ga45722741ea266b4e861938b365c5f362">More...</a><br /></td></tr>
<tr class="separator:ga45722741ea266b4e861938b365c5f362"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1bb55f8963616502e96dc564384d8a03"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga1bb55f8963616502e96dc564384d8a03"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 4, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00248.html#ga1bb55f8963616502e96dc564384d8a03">isnan</a> (qua&lt; T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga1bb55f8963616502e96dc564384d8a03"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns true if x holds a NaN (not a number) representation in the underlying implementation's set of floating point representations.  <a href="a00248.html#ga1bb55f8963616502e96dc564384d8a03">More...</a><br /></td></tr>
<tr class="separator:ga1bb55f8963616502e96dc564384d8a03"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6033dc0741051fa463a0a147ba29f293"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga6033dc0741051fa463a0a147ba29f293"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00248.html#ga6033dc0741051fa463a0a147ba29f293">lerp</a> (qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y, T a)</td></tr>
<tr class="memdesc:ga6033dc0741051fa463a0a147ba29f293"><td class="mdescLeft">&#160;</td><td class="mdescRight">Linear interpolation of two quaternions.  <a href="a00248.html#ga6033dc0741051fa463a0a147ba29f293">More...</a><br /></td></tr>
<tr class="separator:ga6033dc0741051fa463a0a147ba29f293"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafbfe587b8da11fb89a30c3d67dd5ccc2"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gafbfe587b8da11fb89a30c3d67dd5ccc2"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00248.html#gafbfe587b8da11fb89a30c3d67dd5ccc2">mix</a> (qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y, T a)</td></tr>
<tr class="memdesc:gafbfe587b8da11fb89a30c3d67dd5ccc2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spherical linear interpolation of two quaternions.  <a href="a00248.html#gafbfe587b8da11fb89a30c3d67dd5ccc2">More...</a><br /></td></tr>
<tr class="separator:gafbfe587b8da11fb89a30c3d67dd5ccc2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae7fc3c945be366b9942b842f55da428a"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gae7fc3c945be366b9942b842f55da428a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00248.html#gae7fc3c945be366b9942b842f55da428a">slerp</a> (qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y, T a)</td></tr>
<tr class="memdesc:gae7fc3c945be366b9942b842f55da428a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Spherical linear interpolation of two quaternions.  <a href="a00248.html#gae7fc3c945be366b9942b842f55da428a">More...</a><br /></td></tr>
<tr class="separator:gae7fc3c945be366b9942b842f55da428a"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Provides common functions for quaternion types. </p>
<p>Include &lt;<a class="el" href="a00127.html" title="GLM_EXT_quaternion_common ">glm/ext/quaternion_common.hpp</a>&gt; to use the features of this extension.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00258.html" title="Exposes min and max functions for 3 to 4 scalar parameters. ">GLM_EXT_scalar_common</a> </dd>
<dd>
<a class="el" href="a00267.html" title="Exposes min and max functions for 3 to 4 vector parameters. ">GLM_EXT_vector_common</a> </dd>
<dd>
<a class="el" href="a00252.html" title="Exposes single-precision floating point quaternion type. ">GLM_EXT_quaternion_float</a> </dd>
<dd>
<a class="el" href="a00249.html" title="Exposes double-precision floating point quaternion type. ">GLM_EXT_quaternion_double</a> </dd>
<dd>
<a class="el" href="a00251.html" title="Provides exponential functions for quaternion types. ">GLM_EXT_quaternion_exponential</a> </dd>
<dd>
<a class="el" href="a00254.html" title="Provides geometric functions for quaternion types. ">GLM_EXT_quaternion_geometric</a> </dd>
<dd>
<a class="el" href="a00255.html" title="Exposes comparison functions for quaternion types that take a user defined epsilon values...">GLM_EXT_quaternion_relational</a> </dd>
<dd>
<a class="el" href="a00257.html" title="Provides trigonometric functions for quaternion types. ">GLM_EXT_quaternion_trigonometric</a> </dd>
<dd>
<a class="el" href="a00256.html" title="Provides transformation functions for quaternion types. ">GLM_EXT_quaternion_transform</a> </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga10d7bda73201788ac2ab28cd8d0d409b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::conjugate </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the q conjugate. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
    <tr><td class="paramname">Q</td><td>A value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gab41da854ae678e23e114b598cbca4065"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::inverse </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the q inverse. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
    <tr><td class="paramname">Q</td><td>A value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga45722741ea266b4e861938b365c5f362"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, bool, Q&gt; glm::isinf </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns true if x holds a positive infinity or negative infinity representation in the underlying implementation's set of floating point representations. </p>
<p>Returns false otherwise, including for implementations with no infinity representations.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
    <tr><td class="paramname">Q</td><td>A value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga1bb55f8963616502e96dc564384d8a03"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;4, bool, Q&gt; glm::isnan </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns true if x holds a NaN (not a number) representation in the underlying implementation's set of floating point representations. </p>
<p>Returns false otherwise, including for implementations with no NaN representations.</p>
<p>/!\ When using compiler fast math, this function may fail.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
    <tr><td class="paramname">Q</td><td>A value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="ga6033dc0741051fa463a0a147ba29f293"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::lerp </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>a</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Linear interpolation of two quaternions. </p>
<p>The interpolation is oriented.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">x</td><td>A quaternion </td></tr>
    <tr><td class="paramname">y</td><td>A quaternion </td></tr>
    <tr><td class="paramname">a</td><td>Interpolation factor. The interpolation is defined in the range [0, 1].</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
    <tr><td class="paramname">Q</td><td>A value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
<a class="anchor" id="gafbfe587b8da11fb89a30c3d67dd5ccc2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::mix </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>a</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Spherical linear interpolation of two quaternions. </p>
<p>The interpolation is oriented and the rotation is performed at constant speed. For short path spherical linear interpolation, use the slerp function.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">x</td><td>A quaternion </td></tr>
    <tr><td class="paramname">y</td><td>A quaternion </td></tr>
    <tr><td class="paramname">a</td><td>Interpolation factor. The interpolation is defined beyond the range [0, 1].</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
    <tr><td class="paramname">Q</td><td>A value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>- slerp(qua&lt;T, Q&gt; const&amp; x, qua&lt;T, Q&gt; const&amp; y, T const&amp; a) </dd></dl>

</div>
</div>
<a class="anchor" id="gae7fc3c945be366b9942b842f55da428a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::slerp </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>a</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Spherical linear interpolation of two quaternions. </p>
<p>The interpolation always take the short path and the rotation is performed at constant speed.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">x</td><td>A quaternion </td></tr>
    <tr><td class="paramname">y</td><td>A quaternion </td></tr>
    <tr><td class="paramname">a</td><td>Interpolation factor. The interpolation is defined beyond the range [0, 1].</td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>A floating-point scalar type </td></tr>
    <tr><td class="paramname">Q</td><td>A value from qualifier enum </td></tr>
  </table>
  </dd>
</dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
