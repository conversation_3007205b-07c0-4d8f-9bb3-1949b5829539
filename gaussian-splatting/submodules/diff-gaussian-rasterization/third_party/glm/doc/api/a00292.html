<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTC_integer</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTC_integer<div class="ingroups"><a class="el" href="a00286.html">Recommended extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00041.html" title="GLM_GTC_integer ">glm/gtc/integer.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga57824268ebe13a922f1d69a5d37f637f"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga57824268ebe13a922f1d69a5d37f637f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, int, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00292.html#ga57824268ebe13a922f1d69a5d37f637f">iround</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga57824268ebe13a922f1d69a5d37f637f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a value equal to the nearest integer to x.  <a href="a00292.html#ga57824268ebe13a922f1d69a5d37f637f">More...</a><br /></td></tr>
<tr class="separator:ga57824268ebe13a922f1d69a5d37f637f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9bd682e74bfacb005c735305207ec417"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:ga9bd682e74bfacb005c735305207ec417"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00292.html#ga9bd682e74bfacb005c735305207ec417">log2</a> (genIUType x)</td></tr>
<tr class="memdesc:ga9bd682e74bfacb005c735305207ec417"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the log2 of x for integer values.  <a href="a00292.html#ga9bd682e74bfacb005c735305207ec417">More...</a><br /></td></tr>
<tr class="separator:ga9bd682e74bfacb005c735305207ec417"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6715b9d573972a0f7763d30d45bcaec4"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga6715b9d573972a0f7763d30d45bcaec4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, uint, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00292.html#ga6715b9d573972a0f7763d30d45bcaec4">uround</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga6715b9d573972a0f7763d30d45bcaec4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns a value equal to the nearest integer to x.  <a href="a00292.html#ga6715b9d573972a0f7763d30d45bcaec4">More...</a><br /></td></tr>
<tr class="separator:ga6715b9d573972a0f7763d30d45bcaec4"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00041.html" title="GLM_GTC_integer ">glm/gtc/integer.hpp</a>&gt; to use the features of this extension. </p>
<p>Allow to perform bit operations on integer values </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga57824268ebe13a922f1d69a5d37f637f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, int, Q&gt; glm::iround </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns a value equal to the nearest integer to x. </p>
<p>The fraction 0.5 will round in a direction chosen by the implementation, presumably the direction that is fastest.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">x</td><td>The values of the argument must be greater or equal to zero. </td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>floating point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/round.xml">GLSL round man page</a> </dd>
<dd>
<a class="el" href="a00292.html" title="Include <glm/gtc/integer.hpp> to use the features of this extension. ">GLM_GTC_integer</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9bd682e74bfacb005c735305207ec417"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genIUType glm::log2 </td>
          <td>(</td>
          <td class="paramtype">genIUType&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the log2 of x for integer values. </p>
<p>Usefull to compute mipmap count from the texture size. </p><dl class="section see"><dt>See also</dt><dd><a class="el" href="a00292.html" title="Include <glm/gtc/integer.hpp> to use the features of this extension. ">GLM_GTC_integer</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga6715b9d573972a0f7763d30d45bcaec4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, uint, Q&gt; glm::uround </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns a value equal to the nearest integer to x. </p>
<p>The fraction 0.5 will round in a direction chosen by the implementation, presumably the direction that is fastest.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">x</td><td>The values of the argument must be greater or equal to zero. </td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>floating point scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/round.xml">GLSL round man page</a> </dd>
<dd>
<a class="el" href="a00292.html" title="Include <glm/gtc/integer.hpp> to use the features of this extension. ">GLM_GTC_integer</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
