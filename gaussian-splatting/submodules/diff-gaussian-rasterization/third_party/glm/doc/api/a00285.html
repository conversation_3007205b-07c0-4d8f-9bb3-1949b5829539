<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: Stable extensions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#groups">Modules</a>  </div>
  <div class="headertitle">
<div class="title">Stable extensions</div>  </div>
</div><!--header-->
<div class="contents">

<p>Additional features not specified by GLSL specification.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="groups"></a>
Modules</h2></td></tr>
<tr class="memitem:a00243"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00243.html">GLM_EXT_matrix_clip_space</a></td></tr>
<tr class="memdesc:a00243"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines functions that generate clip space transformation matrices. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00244"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00244.html">GLM_EXT_matrix_common</a></td></tr>
<tr class="memdesc:a00244"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines functions for common matrix operations. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00245"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00245.html">GLM_EXT_matrix_projection</a></td></tr>
<tr class="memdesc:a00245"><td class="mdescLeft">&#160;</td><td class="mdescRight">Functions that generate common projection transformation matrices. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00246"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00246.html">GLM_EXT_matrix_relational</a></td></tr>
<tr class="memdesc:a00246"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes comparison functions for matrix types that take a user defined epsilon values. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00247"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00247.html">GLM_EXT_matrix_transform</a></td></tr>
<tr class="memdesc:a00247"><td class="mdescLeft">&#160;</td><td class="mdescRight">Defines functions that generate common transformation matrices. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00248"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00248.html">GLM_EXT_quaternion_common</a></td></tr>
<tr class="memdesc:a00248"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides common functions for quaternion types. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00249"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00249.html">GLM_EXT_quaternion_double</a></td></tr>
<tr class="memdesc:a00249"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes double-precision floating point quaternion type. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00250"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00250.html">GLM_EXT_quaternion_double_precision</a></td></tr>
<tr class="memdesc:a00250"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes double-precision floating point quaternion type with various precision in term of ULPs. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00251"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00251.html">GLM_EXT_quaternion_exponential</a></td></tr>
<tr class="memdesc:a00251"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides exponential functions for quaternion types. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00252"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00252.html">GLM_EXT_quaternion_float</a></td></tr>
<tr class="memdesc:a00252"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes single-precision floating point quaternion type. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00253"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00253.html">GLM_EXT_quaternion_float_precision</a></td></tr>
<tr class="memdesc:a00253"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes single-precision floating point quaternion type with various precision in term of ULPs. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00254"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00254.html">GLM_EXT_quaternion_geometric</a></td></tr>
<tr class="memdesc:a00254"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides geometric functions for quaternion types. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00255"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00255.html">GLM_EXT_quaternion_relational</a></td></tr>
<tr class="memdesc:a00255"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes comparison functions for quaternion types that take a user defined epsilon values. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00256"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00256.html">GLM_EXT_quaternion_transform</a></td></tr>
<tr class="memdesc:a00256"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides transformation functions for quaternion types. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00257"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00257.html">GLM_EXT_quaternion_trigonometric</a></td></tr>
<tr class="memdesc:a00257"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides trigonometric functions for quaternion types. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00258"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00258.html">GLM_EXT_scalar_common</a></td></tr>
<tr class="memdesc:a00258"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes min and max functions for 3 to 4 scalar parameters. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00259"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00259.html">GLM_EXT_scalar_constants</a></td></tr>
<tr class="memdesc:a00259"><td class="mdescLeft">&#160;</td><td class="mdescRight">Provides a list of constants and precomputed useful values. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00260"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00260.html">GLM_EXT_scalar_int_sized</a></td></tr>
<tr class="memdesc:a00260"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes sized signed integer scalar types. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00261"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00261.html">GLM_EXT_scalar_integer</a></td></tr>
<tr class="memdesc:a00261"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00147.html" title="GLM_EXT_scalar_integer ">glm/ext/scalar_integer.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00262"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00262.html">GLM_EXT_scalar_relational</a></td></tr>
<tr class="memdesc:a00262"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes comparison functions for scalar types that take a user defined epsilon values. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00263"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00263.html">GLM_EXT_scalar_uint_sized</a></td></tr>
<tr class="memdesc:a00263"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes sized unsigned integer scalar types. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00264"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00264.html">GLM_EXT_scalar_ulp</a></td></tr>
<tr class="memdesc:a00264"><td class="mdescLeft">&#160;</td><td class="mdescRight">Allow the measurement of the accuracy of a function against a reference implementation. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00265"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00265.html">GLM_EXT_vector_bool1</a></td></tr>
<tr class="memdesc:a00265"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes bvec1 vector type. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00266"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00266.html">GLM_EXT_vector_bool1_precision</a></td></tr>
<tr class="memdesc:a00266"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes highp_bvec1, mediump_bvec1 and lowp_bvec1 types. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00267"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00267.html">GLM_EXT_vector_common</a></td></tr>
<tr class="memdesc:a00267"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes min and max functions for 3 to 4 vector parameters. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00268"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00268.html">GLM_EXT_vector_double1</a></td></tr>
<tr class="memdesc:a00268"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes double-precision floating point vector type with one component. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00269"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00269.html">GLM_EXT_vector_double1_precision</a></td></tr>
<tr class="memdesc:a00269"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes highp_dvec1, mediump_dvec1 and lowp_dvec1 types. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00270"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00270.html">GLM_EXT_vector_float1</a></td></tr>
<tr class="memdesc:a00270"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes single-precision floating point vector type with one component. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00271"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00271.html">GLM_EXT_vector_float1_precision</a></td></tr>
<tr class="memdesc:a00271"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes highp_vec1, mediump_vec1 and lowp_vec1 types. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00272"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00272.html">GLM_EXT_vector_int1</a></td></tr>
<tr class="memdesc:a00272"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes ivec1 vector type. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00273"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00273.html">GLM_EXT_vector_int1_precision</a></td></tr>
<tr class="memdesc:a00273"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes highp_ivec1, mediump_ivec1 and lowp_ivec1 types. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00274"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00274.html">GLM_EXT_vector_integer</a></td></tr>
<tr class="memdesc:a00274"><td class="mdescLeft">&#160;</td><td class="mdescRight">Include &lt;<a class="el" href="a00222.html" title="GLM_EXT_vector_integer ">glm/ext/vector_integer.hpp</a>&gt; to use the features of this extension. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00275"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00275.html">GLM_EXT_vector_relational</a></td></tr>
<tr class="memdesc:a00275"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes comparison functions for vector types that take a user defined epsilon values. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00276"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00276.html">GLM_EXT_vector_uint1</a></td></tr>
<tr class="memdesc:a00276"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes uvec1 vector type. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00277"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00277.html">GLM_EXT_vector_uint1_precision</a></td></tr>
<tr class="memdesc:a00277"><td class="mdescLeft">&#160;</td><td class="mdescRight">Exposes highp_uvec1, mediump_uvec1 and lowp_uvec1 types. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:a00278"><td class="memItemLeft" align="right" valign="top">&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00278.html">GLM_EXT_vector_ulp</a></td></tr>
<tr class="memdesc:a00278"><td class="mdescLeft">&#160;</td><td class="mdescRight">Allow the measurement of the accuracy of a function against a reference implementation. <br /></td></tr>
<tr class="separator:"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Additional features not specified by GLSL specification. </p>
<p>EXT extensions are fully tested and documented.</p>
<p>Even if it's highly unrecommended, it's possible to include all the extensions at once by including &lt;<a class="el" href="a00027.html" title="Core features (Dependence) ">glm/ext.hpp</a>&gt;. Otherwise, each extension needs to be included a specific file. </p>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
