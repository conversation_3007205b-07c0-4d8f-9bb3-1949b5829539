<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTC_epsilon</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTC_epsilon<div class="ingroups"><a class="el" href="a00286.html">Recommended extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00024.html" title="GLM_GTC_epsilon ">glm/gtc/epsilon.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga91b417866cafadd076004778217a1844"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga91b417866cafadd076004778217a1844"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00291.html#ga91b417866cafadd076004778217a1844">epsilonEqual</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, T const &amp;epsilon)</td></tr>
<tr class="memdesc:ga91b417866cafadd076004778217a1844"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of |x - y| &lt; epsilon.  <a href="a00291.html#ga91b417866cafadd076004778217a1844">More...</a><br /></td></tr>
<tr class="separator:ga91b417866cafadd076004778217a1844"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa7f227999ca09e7ca994e8b35aba47bb"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gaa7f227999ca09e7ca994e8b35aba47bb"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00291.html#gaa7f227999ca09e7ca994e8b35aba47bb">epsilonEqual</a> (genType const &amp;x, genType const &amp;y, genType const &amp;epsilon)</td></tr>
<tr class="memdesc:gaa7f227999ca09e7ca994e8b35aba47bb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of |x - y| &lt; epsilon.  <a href="a00291.html#gaa7f227999ca09e7ca994e8b35aba47bb">More...</a><br /></td></tr>
<tr class="separator:gaa7f227999ca09e7ca994e8b35aba47bb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf840d33b9a5261ec78dcd5125743b025"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf840d33b9a5261ec78dcd5125743b025"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, bool, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00291.html#gaf840d33b9a5261ec78dcd5125743b025">epsilonNotEqual</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;y, T const &amp;epsilon)</td></tr>
<tr class="memdesc:gaf840d33b9a5261ec78dcd5125743b025"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of |x - y| &lt; epsilon.  <a href="a00291.html#gaf840d33b9a5261ec78dcd5125743b025">More...</a><br /></td></tr>
<tr class="separator:gaf840d33b9a5261ec78dcd5125743b025"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga50a92103fb0cbd796908e1bf20c79aaf"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga50a92103fb0cbd796908e1bf20c79aaf"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00291.html#ga50a92103fb0cbd796908e1bf20c79aaf">epsilonNotEqual</a> (genType const &amp;x, genType const &amp;y, genType const &amp;epsilon)</td></tr>
<tr class="memdesc:ga50a92103fb0cbd796908e1bf20c79aaf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the component-wise comparison of |x - y| &gt;= epsilon.  <a href="a00291.html#ga50a92103fb0cbd796908e1bf20c79aaf">More...</a><br /></td></tr>
<tr class="separator:ga50a92103fb0cbd796908e1bf20c79aaf"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00024.html" title="GLM_GTC_epsilon ">glm/gtc/epsilon.hpp</a>&gt; to use the features of this extension. </p>
<p>Comparison functions for a user defined epsilon values. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga91b417866cafadd076004778217a1844"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, bool, Q&gt; glm::epsilonEqual </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>epsilon</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the component-wise comparison of |x - y| &lt; epsilon. </p>
<p>True if this expression is satisfied.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00291.html" title="Include <glm/gtc/epsilon.hpp> to use the features of this extension. ">GLM_GTC_epsilon</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaa7f227999ca09e7ca994e8b35aba47bb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL bool glm::epsilonEqual </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>epsilon</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the component-wise comparison of |x - y| &lt; epsilon. </p>
<p>True if this expression is satisfied.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00291.html" title="Include <glm/gtc/epsilon.hpp> to use the features of this extension. ">GLM_GTC_epsilon</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaf840d33b9a5261ec78dcd5125743b025"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, bool, Q&gt; glm::epsilonNotEqual </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>epsilon</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the component-wise comparison of |x - y| &lt; epsilon. </p>
<p>True if this expression is not satisfied.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00291.html" title="Include <glm/gtc/epsilon.hpp> to use the features of this extension. ">GLM_GTC_epsilon</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga50a92103fb0cbd796908e1bf20c79aaf"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL bool glm::epsilonNotEqual </td>
          <td>(</td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType const &amp;&#160;</td>
          <td class="paramname"><em>epsilon</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the component-wise comparison of |x - y| &gt;= epsilon. </p>
<p>True if this expression is not satisfied.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00291.html" title="Include <glm/gtc/epsilon.hpp> to use the features of this extension. ">GLM_GTC_epsilon</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
