<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_euler_angles</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_euler_angles<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00025.html" title="GLM_GTX_euler_angles ">glm/gtx/euler_angles.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga994b8186b3b80d91cf90bc403164692f"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga994b8186b3b80d91cf90bc403164692f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga994b8186b3b80d91cf90bc403164692f">derivedEulerAngleX</a> (T const &amp;angleX, T const &amp;angularVelocityX)</td></tr>
<tr class="memdesc:ga994b8186b3b80d91cf90bc403164692f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous derived matrix from the rotation matrix about X-axis.  <a href="a00319.html#ga994b8186b3b80d91cf90bc403164692f">More...</a><br /></td></tr>
<tr class="separator:ga994b8186b3b80d91cf90bc403164692f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0a4c56ecce7abcb69508ebe6313e9d10"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga0a4c56ecce7abcb69508ebe6313e9d10"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga0a4c56ecce7abcb69508ebe6313e9d10">derivedEulerAngleY</a> (T const &amp;angleY, T const &amp;angularVelocityY)</td></tr>
<tr class="memdesc:ga0a4c56ecce7abcb69508ebe6313e9d10"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous derived matrix from the rotation matrix about Y-axis.  <a href="a00319.html#ga0a4c56ecce7abcb69508ebe6313e9d10">More...</a><br /></td></tr>
<tr class="separator:ga0a4c56ecce7abcb69508ebe6313e9d10"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae8b397348201c42667be983ba3f344df"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gae8b397348201c42667be983ba3f344df"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#gae8b397348201c42667be983ba3f344df">derivedEulerAngleZ</a> (T const &amp;angleZ, T const &amp;angularVelocityZ)</td></tr>
<tr class="memdesc:gae8b397348201c42667be983ba3f344df"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous derived matrix from the rotation matrix about Z-axis.  <a href="a00319.html#gae8b397348201c42667be983ba3f344df">More...</a><br /></td></tr>
<tr class="separator:gae8b397348201c42667be983ba3f344df"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafba6282e4ed3ff8b5c75331abfba3489"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gafba6282e4ed3ff8b5c75331abfba3489"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#gafba6282e4ed3ff8b5c75331abfba3489">eulerAngleX</a> (T const &amp;angleX)</td></tr>
<tr class="memdesc:gafba6282e4ed3ff8b5c75331abfba3489"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from an euler angle X.  <a href="a00319.html#gafba6282e4ed3ff8b5c75331abfba3489">More...</a><br /></td></tr>
<tr class="separator:gafba6282e4ed3ff8b5c75331abfba3489"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga64036577ee17a2d24be0dbc05881d4e2"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga64036577ee17a2d24be0dbc05881d4e2"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga64036577ee17a2d24be0dbc05881d4e2">eulerAngleXY</a> (T const &amp;angleX, T const &amp;angleY)</td></tr>
<tr class="memdesc:ga64036577ee17a2d24be0dbc05881d4e2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (X * Y).  <a href="a00319.html#ga64036577ee17a2d24be0dbc05881d4e2">More...</a><br /></td></tr>
<tr class="separator:ga64036577ee17a2d24be0dbc05881d4e2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga29bd0787a28a6648159c0d6e69706066"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga29bd0787a28a6648159c0d6e69706066"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga29bd0787a28a6648159c0d6e69706066">eulerAngleXYX</a> (T const &amp;t1, T const &amp;t2, T const &amp;t3)</td></tr>
<tr class="memdesc:ga29bd0787a28a6648159c0d6e69706066"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (X * Y * X).  <a href="a00319.html#ga29bd0787a28a6648159c0d6e69706066">More...</a><br /></td></tr>
<tr class="separator:ga29bd0787a28a6648159c0d6e69706066"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1975e0f0e9bed7f716dc9946da2ab645"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga1975e0f0e9bed7f716dc9946da2ab645"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga1975e0f0e9bed7f716dc9946da2ab645">eulerAngleXYZ</a> (T const &amp;t1, T const &amp;t2, T const &amp;t3)</td></tr>
<tr class="memdesc:ga1975e0f0e9bed7f716dc9946da2ab645"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (X * Y * Z).  <a href="a00319.html#ga1975e0f0e9bed7f716dc9946da2ab645">More...</a><br /></td></tr>
<tr class="separator:ga1975e0f0e9bed7f716dc9946da2ab645"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa39bd323c65c2fc0a1508be33a237ce9"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaa39bd323c65c2fc0a1508be33a237ce9"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#gaa39bd323c65c2fc0a1508be33a237ce9">eulerAngleXZ</a> (T const &amp;angleX, T const &amp;angleZ)</td></tr>
<tr class="memdesc:gaa39bd323c65c2fc0a1508be33a237ce9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (X * Z).  <a href="a00319.html#gaa39bd323c65c2fc0a1508be33a237ce9">More...</a><br /></td></tr>
<tr class="separator:gaa39bd323c65c2fc0a1508be33a237ce9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga60171c79a17aec85d7891ae1d1533ec9"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga60171c79a17aec85d7891ae1d1533ec9"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga60171c79a17aec85d7891ae1d1533ec9">eulerAngleXZX</a> (T const &amp;t1, T const &amp;t2, T const &amp;t3)</td></tr>
<tr class="memdesc:ga60171c79a17aec85d7891ae1d1533ec9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (X * Z * X).  <a href="a00319.html#ga60171c79a17aec85d7891ae1d1533ec9">More...</a><br /></td></tr>
<tr class="separator:ga60171c79a17aec85d7891ae1d1533ec9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga996dce12a60d8a674ba6737a535fa910"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga996dce12a60d8a674ba6737a535fa910"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga996dce12a60d8a674ba6737a535fa910">eulerAngleXZY</a> (T const &amp;t1, T const &amp;t2, T const &amp;t3)</td></tr>
<tr class="memdesc:ga996dce12a60d8a674ba6737a535fa910"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (X * Z * Y).  <a href="a00319.html#ga996dce12a60d8a674ba6737a535fa910">More...</a><br /></td></tr>
<tr class="separator:ga996dce12a60d8a674ba6737a535fa910"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab84bf4746805fd69b8ecbb230e3974c5"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gab84bf4746805fd69b8ecbb230e3974c5"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#gab84bf4746805fd69b8ecbb230e3974c5">eulerAngleY</a> (T const &amp;angleY)</td></tr>
<tr class="memdesc:gab84bf4746805fd69b8ecbb230e3974c5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from an euler angle Y.  <a href="a00319.html#gab84bf4746805fd69b8ecbb230e3974c5">More...</a><br /></td></tr>
<tr class="separator:gab84bf4746805fd69b8ecbb230e3974c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4f57e6dd25c3cffbbd4daa6ef3f4486d"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga4f57e6dd25c3cffbbd4daa6ef3f4486d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga4f57e6dd25c3cffbbd4daa6ef3f4486d">eulerAngleYX</a> (T const &amp;angleY, T const &amp;angleX)</td></tr>
<tr class="memdesc:ga4f57e6dd25c3cffbbd4daa6ef3f4486d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Y * X).  <a href="a00319.html#ga4f57e6dd25c3cffbbd4daa6ef3f4486d">More...</a><br /></td></tr>
<tr class="separator:ga4f57e6dd25c3cffbbd4daa6ef3f4486d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga750fba9894117f87bcc529d7349d11de"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga750fba9894117f87bcc529d7349d11de"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga750fba9894117f87bcc529d7349d11de">eulerAngleYXY</a> (T const &amp;t1, T const &amp;t2, T const &amp;t3)</td></tr>
<tr class="memdesc:ga750fba9894117f87bcc529d7349d11de"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Y * X * Y).  <a href="a00319.html#ga750fba9894117f87bcc529d7349d11de">More...</a><br /></td></tr>
<tr class="separator:ga750fba9894117f87bcc529d7349d11de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab8ba99a9814f6d9edf417b6c6d5b0c10"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gab8ba99a9814f6d9edf417b6c6d5b0c10"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#gab8ba99a9814f6d9edf417b6c6d5b0c10">eulerAngleYXZ</a> (T const &amp;yaw, T const &amp;pitch, T const &amp;roll)</td></tr>
<tr class="memdesc:gab8ba99a9814f6d9edf417b6c6d5b0c10"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Y * X * Z).  <a href="a00319.html#gab8ba99a9814f6d9edf417b6c6d5b0c10">More...</a><br /></td></tr>
<tr class="separator:gab8ba99a9814f6d9edf417b6c6d5b0c10"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga220379e10ac8cca55e275f0c9018fed9"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga220379e10ac8cca55e275f0c9018fed9"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga220379e10ac8cca55e275f0c9018fed9">eulerAngleYZ</a> (T const &amp;angleY, T const &amp;angleZ)</td></tr>
<tr class="memdesc:ga220379e10ac8cca55e275f0c9018fed9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Y * Z).  <a href="a00319.html#ga220379e10ac8cca55e275f0c9018fed9">More...</a><br /></td></tr>
<tr class="separator:ga220379e10ac8cca55e275f0c9018fed9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga08bef16357b8f9b3051b3dcaec4b7848"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga08bef16357b8f9b3051b3dcaec4b7848"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga08bef16357b8f9b3051b3dcaec4b7848">eulerAngleYZX</a> (T const &amp;t1, T const &amp;t2, T const &amp;t3)</td></tr>
<tr class="memdesc:ga08bef16357b8f9b3051b3dcaec4b7848"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Y * Z * X).  <a href="a00319.html#ga08bef16357b8f9b3051b3dcaec4b7848">More...</a><br /></td></tr>
<tr class="separator:ga08bef16357b8f9b3051b3dcaec4b7848"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5e5e40abc27630749b42b3327c76d6e4"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga5e5e40abc27630749b42b3327c76d6e4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga5e5e40abc27630749b42b3327c76d6e4">eulerAngleYZY</a> (T const &amp;t1, T const &amp;t2, T const &amp;t3)</td></tr>
<tr class="memdesc:ga5e5e40abc27630749b42b3327c76d6e4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Y * Z * Y).  <a href="a00319.html#ga5e5e40abc27630749b42b3327c76d6e4">More...</a><br /></td></tr>
<tr class="separator:ga5e5e40abc27630749b42b3327c76d6e4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5b3935248bb6c3ec6b0d9297d406e251"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga5b3935248bb6c3ec6b0d9297d406e251"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga5b3935248bb6c3ec6b0d9297d406e251">eulerAngleZ</a> (T const &amp;angleZ)</td></tr>
<tr class="memdesc:ga5b3935248bb6c3ec6b0d9297d406e251"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from an euler angle Z.  <a href="a00319.html#ga5b3935248bb6c3ec6b0d9297d406e251">More...</a><br /></td></tr>
<tr class="separator:ga5b3935248bb6c3ec6b0d9297d406e251"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga483903115cd4059228961046a28d69b5"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga483903115cd4059228961046a28d69b5"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga483903115cd4059228961046a28d69b5">eulerAngleZX</a> (T const &amp;angle, T const &amp;angleX)</td></tr>
<tr class="memdesc:ga483903115cd4059228961046a28d69b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Z * X).  <a href="a00319.html#ga483903115cd4059228961046a28d69b5">More...</a><br /></td></tr>
<tr class="separator:ga483903115cd4059228961046a28d69b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab4505c54d2dd654df4569fd1f04c43aa"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gab4505c54d2dd654df4569fd1f04c43aa"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#gab4505c54d2dd654df4569fd1f04c43aa">eulerAngleZXY</a> (T const &amp;t1, T const &amp;t2, T const &amp;t3)</td></tr>
<tr class="memdesc:gab4505c54d2dd654df4569fd1f04c43aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Z * X * Y).  <a href="a00319.html#gab4505c54d2dd654df4569fd1f04c43aa">More...</a><br /></td></tr>
<tr class="separator:gab4505c54d2dd654df4569fd1f04c43aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga178f966c52b01e4d65e31ebd007e3247"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga178f966c52b01e4d65e31ebd007e3247"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga178f966c52b01e4d65e31ebd007e3247">eulerAngleZXZ</a> (T const &amp;t1, T const &amp;t2, T const &amp;t3)</td></tr>
<tr class="memdesc:ga178f966c52b01e4d65e31ebd007e3247"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Z * X * Z).  <a href="a00319.html#ga178f966c52b01e4d65e31ebd007e3247">More...</a><br /></td></tr>
<tr class="separator:ga178f966c52b01e4d65e31ebd007e3247"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga400b2bd5984999efab663f3a68e1d020"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga400b2bd5984999efab663f3a68e1d020"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga400b2bd5984999efab663f3a68e1d020">eulerAngleZY</a> (T const &amp;angleZ, T const &amp;angleY)</td></tr>
<tr class="memdesc:ga400b2bd5984999efab663f3a68e1d020"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Z * Y).  <a href="a00319.html#ga400b2bd5984999efab663f3a68e1d020">More...</a><br /></td></tr>
<tr class="separator:ga400b2bd5984999efab663f3a68e1d020"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2e61f1e39069c47530acab9167852dd6"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga2e61f1e39069c47530acab9167852dd6"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga2e61f1e39069c47530acab9167852dd6">eulerAngleZYX</a> (T const &amp;t1, T const &amp;t2, T const &amp;t3)</td></tr>
<tr class="memdesc:ga2e61f1e39069c47530acab9167852dd6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Z * Y * X).  <a href="a00319.html#ga2e61f1e39069c47530acab9167852dd6">More...</a><br /></td></tr>
<tr class="separator:ga2e61f1e39069c47530acab9167852dd6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacd795f1dbecaf74974f9c76bbcca6830"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gacd795f1dbecaf74974f9c76bbcca6830"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#gacd795f1dbecaf74974f9c76bbcca6830">eulerAngleZYZ</a> (T const &amp;t1, T const &amp;t2, T const &amp;t3)</td></tr>
<tr class="memdesc:gacd795f1dbecaf74974f9c76bbcca6830"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Z * Y * Z).  <a href="a00319.html#gacd795f1dbecaf74974f9c76bbcca6830">More...</a><br /></td></tr>
<tr class="separator:gacd795f1dbecaf74974f9c76bbcca6830"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf1077a72171d0f3b08f022ab5ff88af7"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaf1077a72171d0f3b08f022ab5ff88af7"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#gaf1077a72171d0f3b08f022ab5ff88af7">extractEulerAngleXYX</a> (mat&lt; 4, 4, T, defaultp &gt; const &amp;M, T &amp;t1, T &amp;t2, T &amp;t3)</td></tr>
<tr class="memdesc:gaf1077a72171d0f3b08f022ab5ff88af7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extracts the (X * Y * X) Euler angles from the rotation matrix M.  <a href="a00319.html#gaf1077a72171d0f3b08f022ab5ff88af7">More...</a><br /></td></tr>
<tr class="separator:gaf1077a72171d0f3b08f022ab5ff88af7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacea701562f778c1da4d3a0a1cf091000"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gacea701562f778c1da4d3a0a1cf091000"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#gacea701562f778c1da4d3a0a1cf091000">extractEulerAngleXYZ</a> (mat&lt; 4, 4, T, defaultp &gt; const &amp;M, T &amp;t1, T &amp;t2, T &amp;t3)</td></tr>
<tr class="memdesc:gacea701562f778c1da4d3a0a1cf091000"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extracts the (X * Y * Z) Euler angles from the rotation matrix M.  <a href="a00319.html#gacea701562f778c1da4d3a0a1cf091000">More...</a><br /></td></tr>
<tr class="separator:gacea701562f778c1da4d3a0a1cf091000"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacf0bc6c031f25fa3ee0055b62c8260d0"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gacf0bc6c031f25fa3ee0055b62c8260d0"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#gacf0bc6c031f25fa3ee0055b62c8260d0">extractEulerAngleXZX</a> (mat&lt; 4, 4, T, defaultp &gt; const &amp;M, T &amp;t1, T &amp;t2, T &amp;t3)</td></tr>
<tr class="memdesc:gacf0bc6c031f25fa3ee0055b62c8260d0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extracts the (X * Z * X) Euler angles from the rotation matrix M.  <a href="a00319.html#gacf0bc6c031f25fa3ee0055b62c8260d0">More...</a><br /></td></tr>
<tr class="separator:gacf0bc6c031f25fa3ee0055b62c8260d0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabe5a65d8eb1cd873c8de121cce1a15ed"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gabe5a65d8eb1cd873c8de121cce1a15ed"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#gabe5a65d8eb1cd873c8de121cce1a15ed">extractEulerAngleXZY</a> (mat&lt; 4, 4, T, defaultp &gt; const &amp;M, T &amp;t1, T &amp;t2, T &amp;t3)</td></tr>
<tr class="memdesc:gabe5a65d8eb1cd873c8de121cce1a15ed"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extracts the (X * Z * Y) Euler angles from the rotation matrix M.  <a href="a00319.html#gabe5a65d8eb1cd873c8de121cce1a15ed">More...</a><br /></td></tr>
<tr class="separator:gabe5a65d8eb1cd873c8de121cce1a15ed"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaab8868556361a190db94374e9983ed39"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaab8868556361a190db94374e9983ed39"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#gaab8868556361a190db94374e9983ed39">extractEulerAngleYXY</a> (mat&lt; 4, 4, T, defaultp &gt; const &amp;M, T &amp;t1, T &amp;t2, T &amp;t3)</td></tr>
<tr class="memdesc:gaab8868556361a190db94374e9983ed39"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extracts the (Y * X * Y) Euler angles from the rotation matrix M.  <a href="a00319.html#gaab8868556361a190db94374e9983ed39">More...</a><br /></td></tr>
<tr class="separator:gaab8868556361a190db94374e9983ed39"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf0937518e63037335a0e8358b6f053c5"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gaf0937518e63037335a0e8358b6f053c5"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#gaf0937518e63037335a0e8358b6f053c5">extractEulerAngleYXZ</a> (mat&lt; 4, 4, T, defaultp &gt; const &amp;M, T &amp;t1, T &amp;t2, T &amp;t3)</td></tr>
<tr class="memdesc:gaf0937518e63037335a0e8358b6f053c5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extracts the (Y * X * Z) Euler angles from the rotation matrix M.  <a href="a00319.html#gaf0937518e63037335a0e8358b6f053c5">More...</a><br /></td></tr>
<tr class="separator:gaf0937518e63037335a0e8358b6f053c5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9049b78466796c0de2971756e25b93d3"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga9049b78466796c0de2971756e25b93d3"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga9049b78466796c0de2971756e25b93d3">extractEulerAngleYZX</a> (mat&lt; 4, 4, T, defaultp &gt; const &amp;M, T &amp;t1, T &amp;t2, T &amp;t3)</td></tr>
<tr class="memdesc:ga9049b78466796c0de2971756e25b93d3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extracts the (Y * Z * X) Euler angles from the rotation matrix M.  <a href="a00319.html#ga9049b78466796c0de2971756e25b93d3">More...</a><br /></td></tr>
<tr class="separator:ga9049b78466796c0de2971756e25b93d3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga11dad972c109e4bf8694c915017c44a6"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga11dad972c109e4bf8694c915017c44a6"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga11dad972c109e4bf8694c915017c44a6">extractEulerAngleYZY</a> (mat&lt; 4, 4, T, defaultp &gt; const &amp;M, T &amp;t1, T &amp;t2, T &amp;t3)</td></tr>
<tr class="memdesc:ga11dad972c109e4bf8694c915017c44a6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extracts the (Y * Z * Y) Euler angles from the rotation matrix M.  <a href="a00319.html#ga11dad972c109e4bf8694c915017c44a6">More...</a><br /></td></tr>
<tr class="separator:ga11dad972c109e4bf8694c915017c44a6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga81fbbca2ba0c778b9662d5355b4e2363"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga81fbbca2ba0c778b9662d5355b4e2363"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga81fbbca2ba0c778b9662d5355b4e2363">extractEulerAngleZXY</a> (mat&lt; 4, 4, T, defaultp &gt; const &amp;M, T &amp;t1, T &amp;t2, T &amp;t3)</td></tr>
<tr class="memdesc:ga81fbbca2ba0c778b9662d5355b4e2363"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extracts the (Z * X * Y) Euler angles from the rotation matrix M.  <a href="a00319.html#ga81fbbca2ba0c778b9662d5355b4e2363">More...</a><br /></td></tr>
<tr class="separator:ga81fbbca2ba0c778b9662d5355b4e2363"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga59359fef9bad92afaca55e193f91e702"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga59359fef9bad92afaca55e193f91e702"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga59359fef9bad92afaca55e193f91e702">extractEulerAngleZXZ</a> (mat&lt; 4, 4, T, defaultp &gt; const &amp;M, T &amp;t1, T &amp;t2, T &amp;t3)</td></tr>
<tr class="memdesc:ga59359fef9bad92afaca55e193f91e702"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extracts the (Z * X * Z) Euler angles from the rotation matrix M.  <a href="a00319.html#ga59359fef9bad92afaca55e193f91e702">More...</a><br /></td></tr>
<tr class="separator:ga59359fef9bad92afaca55e193f91e702"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2d6c11a4abfa60c565483cee2d3f7665"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga2d6c11a4abfa60c565483cee2d3f7665"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga2d6c11a4abfa60c565483cee2d3f7665">extractEulerAngleZYX</a> (mat&lt; 4, 4, T, defaultp &gt; const &amp;M, T &amp;t1, T &amp;t2, T &amp;t3)</td></tr>
<tr class="memdesc:ga2d6c11a4abfa60c565483cee2d3f7665"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extracts the (Z * Y * X) Euler angles from the rotation matrix M.  <a href="a00319.html#ga2d6c11a4abfa60c565483cee2d3f7665">More...</a><br /></td></tr>
<tr class="separator:ga2d6c11a4abfa60c565483cee2d3f7665"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafdfa880a64b565223550c2d3938b1aeb"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gafdfa880a64b565223550c2d3938b1aeb"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL void&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#gafdfa880a64b565223550c2d3938b1aeb">extractEulerAngleZYZ</a> (mat&lt; 4, 4, T, defaultp &gt; const &amp;M, T &amp;t1, T &amp;t2, T &amp;t3)</td></tr>
<tr class="memdesc:gafdfa880a64b565223550c2d3938b1aeb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Extracts the (Z * Y * Z) Euler angles from the rotation matrix M.  <a href="a00319.html#gafdfa880a64b565223550c2d3938b1aeb">More...</a><br /></td></tr>
<tr class="separator:gafdfa880a64b565223550c2d3938b1aeb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae16738a9f1887cf4e4db6a124637608d"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gae16738a9f1887cf4e4db6a124637608d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 2, 2, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#gae16738a9f1887cf4e4db6a124637608d">orientate2</a> (T const &amp;angle)</td></tr>
<tr class="memdesc:gae16738a9f1887cf4e4db6a124637608d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 2D 2 * 2 rotation matrix from an euler angle.  <a href="a00319.html#gae16738a9f1887cf4e4db6a124637608d">More...</a><br /></td></tr>
<tr class="separator:gae16738a9f1887cf4e4db6a124637608d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7ca98668a5786f19c7b38299ebbc9b4c"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga7ca98668a5786f19c7b38299ebbc9b4c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 3, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga7ca98668a5786f19c7b38299ebbc9b4c">orientate3</a> (T const &amp;angle)</td></tr>
<tr class="memdesc:ga7ca98668a5786f19c7b38299ebbc9b4c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 2D 4 * 4 homogeneous rotation matrix from an euler angle.  <a href="a00319.html#ga7ca98668a5786f19c7b38299ebbc9b4c">More...</a><br /></td></tr>
<tr class="separator:ga7ca98668a5786f19c7b38299ebbc9b4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7238c8e15c7720e3ca6a45ab151eeabb"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga7238c8e15c7720e3ca6a45ab151eeabb"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga7238c8e15c7720e3ca6a45ab151eeabb">orientate3</a> (vec&lt; 3, T, Q &gt; const &amp;angles)</td></tr>
<tr class="memdesc:ga7238c8e15c7720e3ca6a45ab151eeabb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 3 * 3 rotation matrix from euler angles (Y * X * Z).  <a href="a00319.html#ga7238c8e15c7720e3ca6a45ab151eeabb">More...</a><br /></td></tr>
<tr class="separator:ga7238c8e15c7720e3ca6a45ab151eeabb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4a044653f71a4ecec68e0b623382b48a"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga4a044653f71a4ecec68e0b623382b48a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#ga4a044653f71a4ecec68e0b623382b48a">orientate4</a> (vec&lt; 3, T, Q &gt; const &amp;angles)</td></tr>
<tr class="memdesc:ga4a044653f71a4ecec68e0b623382b48a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Y * X * Z).  <a href="a00319.html#ga4a044653f71a4ecec68e0b623382b48a">More...</a><br /></td></tr>
<tr class="separator:ga4a044653f71a4ecec68e0b623382b48a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae6aa26ccb020d281b449619e419a609e"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:gae6aa26ccb020d281b449619e419a609e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, defaultp &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00319.html#gae6aa26ccb020d281b449619e419a609e">yawPitchRoll</a> (T const &amp;yaw, T const &amp;pitch, T const &amp;roll)</td></tr>
<tr class="memdesc:gae6aa26ccb020d281b449619e419a609e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Y * X * Z).  <a href="a00319.html#gae6aa26ccb020d281b449619e419a609e">More...</a><br /></td></tr>
<tr class="separator:gae6aa26ccb020d281b449619e419a609e"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00025.html" title="GLM_GTX_euler_angles ">glm/gtx/euler_angles.hpp</a>&gt; to use the features of this extension. </p>
<p>Build matrices from Euler angles.</p>
<p>Extraction of Euler angles from rotation matrix. Based on the original paper 2014 Mike Day - Extracting Euler Angles from a Rotation Matrix. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga994b8186b3b80d91cf90bc403164692f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::derivedEulerAngleX </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angleX</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angularVelocityX</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous derived matrix from the rotation matrix about X-axis. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga0a4c56ecce7abcb69508ebe6313e9d10"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::derivedEulerAngleY </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angleY</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angularVelocityY</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous derived matrix from the rotation matrix about Y-axis. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae8b397348201c42667be983ba3f344df"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::derivedEulerAngleZ </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angleZ</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angularVelocityZ</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous derived matrix from the rotation matrix about Z-axis. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gafba6282e4ed3ff8b5c75331abfba3489"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::eulerAngleX </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angleX</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from an euler angle X. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga64036577ee17a2d24be0dbc05881d4e2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::eulerAngleXY </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angleX</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angleY</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (X * Y). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga29bd0787a28a6648159c0d6e69706066"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::eulerAngleXYX </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (X * Y * X). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga1975e0f0e9bed7f716dc9946da2ab645"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::eulerAngleXYZ </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (X * Y * Z). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaa39bd323c65c2fc0a1508be33a237ce9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::eulerAngleXZ </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angleX</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angleZ</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (X * Z). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga60171c79a17aec85d7891ae1d1533ec9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::eulerAngleXZX </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (X * Z * X). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga996dce12a60d8a674ba6737a535fa910"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::eulerAngleXZY </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (X * Z * Y). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab84bf4746805fd69b8ecbb230e3974c5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::eulerAngleY </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angleY</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from an euler angle Y. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga4f57e6dd25c3cffbbd4daa6ef3f4486d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::eulerAngleYX </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angleY</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angleX</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Y * X). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga750fba9894117f87bcc529d7349d11de"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::eulerAngleYXY </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Y * X * Y). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab8ba99a9814f6d9edf417b6c6d5b0c10"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::eulerAngleYXZ </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>yaw</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>pitch</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>roll</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Y * X * Z). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga220379e10ac8cca55e275f0c9018fed9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::eulerAngleYZ </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angleY</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angleZ</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Y * Z). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga08bef16357b8f9b3051b3dcaec4b7848"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::eulerAngleYZX </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Y * Z * X). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5e5e40abc27630749b42b3327c76d6e4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::eulerAngleYZY </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Y * Z * Y). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga5b3935248bb6c3ec6b0d9297d406e251"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::eulerAngleZ </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angleZ</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from an euler angle Z. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga483903115cd4059228961046a28d69b5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::eulerAngleZX </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angle</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angleX</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Z * X). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab4505c54d2dd654df4569fd1f04c43aa"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::eulerAngleZXY </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Z * X * Y). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga178f966c52b01e4d65e31ebd007e3247"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::eulerAngleZXZ </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Z * X * Z). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga400b2bd5984999efab663f3a68e1d020"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::eulerAngleZY </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angleZ</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angleY</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Z * Y). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2e61f1e39069c47530acab9167852dd6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::eulerAngleZYX </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Z * Y * X). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gacd795f1dbecaf74974f9c76bbcca6830"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::eulerAngleZYZ </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Z * Y * Z). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaf1077a72171d0f3b08f022ab5ff88af7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL void glm::extractEulerAngleXYX </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, defaultp &gt; const &amp;&#160;</td>
          <td class="paramname"><em>M</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Extracts the (X * Y * X) Euler angles from the rotation matrix M. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gacea701562f778c1da4d3a0a1cf091000"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL void glm::extractEulerAngleXYZ </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, defaultp &gt; const &amp;&#160;</td>
          <td class="paramname"><em>M</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Extracts the (X * Y * Z) Euler angles from the rotation matrix M. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gacf0bc6c031f25fa3ee0055b62c8260d0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL void glm::extractEulerAngleXZX </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, defaultp &gt; const &amp;&#160;</td>
          <td class="paramname"><em>M</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Extracts the (X * Z * X) Euler angles from the rotation matrix M. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gabe5a65d8eb1cd873c8de121cce1a15ed"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL void glm::extractEulerAngleXZY </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, defaultp &gt; const &amp;&#160;</td>
          <td class="paramname"><em>M</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Extracts the (X * Z * Y) Euler angles from the rotation matrix M. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaab8868556361a190db94374e9983ed39"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL void glm::extractEulerAngleYXY </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, defaultp &gt; const &amp;&#160;</td>
          <td class="paramname"><em>M</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Extracts the (Y * X * Y) Euler angles from the rotation matrix M. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaf0937518e63037335a0e8358b6f053c5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL void glm::extractEulerAngleYXZ </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, defaultp &gt; const &amp;&#160;</td>
          <td class="paramname"><em>M</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Extracts the (Y * X * Z) Euler angles from the rotation matrix M. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9049b78466796c0de2971756e25b93d3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL void glm::extractEulerAngleYZX </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, defaultp &gt; const &amp;&#160;</td>
          <td class="paramname"><em>M</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Extracts the (Y * Z * X) Euler angles from the rotation matrix M. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga11dad972c109e4bf8694c915017c44a6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL void glm::extractEulerAngleYZY </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, defaultp &gt; const &amp;&#160;</td>
          <td class="paramname"><em>M</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Extracts the (Y * Z * Y) Euler angles from the rotation matrix M. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga81fbbca2ba0c778b9662d5355b4e2363"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL void glm::extractEulerAngleZXY </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, defaultp &gt; const &amp;&#160;</td>
          <td class="paramname"><em>M</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Extracts the (Z * X * Y) Euler angles from the rotation matrix M. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga59359fef9bad92afaca55e193f91e702"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL void glm::extractEulerAngleZXZ </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, defaultp &gt; const &amp;&#160;</td>
          <td class="paramname"><em>M</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Extracts the (Z * X * Z) Euler angles from the rotation matrix M. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2d6c11a4abfa60c565483cee2d3f7665"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL void glm::extractEulerAngleZYX </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, defaultp &gt; const &amp;&#160;</td>
          <td class="paramname"><em>M</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Extracts the (Z * Y * X) Euler angles from the rotation matrix M. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gafdfa880a64b565223550c2d3938b1aeb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL void glm::extractEulerAngleZYZ </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, defaultp &gt; const &amp;&#160;</td>
          <td class="paramname"><em>M</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t2</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T &amp;&#160;</td>
          <td class="paramname"><em>t3</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Extracts the (Z * Y * Z) Euler angles from the rotation matrix M. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae16738a9f1887cf4e4db6a124637608d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;2, 2, T, defaultp&gt; glm::orientate2 </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 2D 2 * 2 rotation matrix from an euler angle. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7ca98668a5786f19c7b38299ebbc9b4c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;3, 3, T, defaultp&gt; glm::orientate3 </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>angle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 2D 4 * 4 homogeneous rotation matrix from an euler angle. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7238c8e15c7720e3ca6a45ab151eeabb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;3, 3, T, Q&gt; glm::orientate3 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>angles</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 3 * 3 rotation matrix from euler angles (Y * X * Z). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga4a044653f71a4ecec68e0b623382b48a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::orientate4 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>angles</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Y * X * Z). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae6aa26ccb020d281b449619e419a609e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, defaultp&gt; glm::yawPitchRoll </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>yaw</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>pitch</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>roll</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Creates a 3D 4 * 4 homogeneous rotation matrix from euler angles (Y * X * Z). </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00319.html" title="Include <glm/gtx/euler_angles.hpp> to use the features of this extension. ">GLM_GTX_euler_angles</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
