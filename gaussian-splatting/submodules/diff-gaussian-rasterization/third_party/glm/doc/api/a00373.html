<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: Angle and Trigonometry Functions</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">Angle and Trigonometry Functions<div class="ingroups"><a class="el" href="a00280.html">Core features</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Function parameters specified as angle are assumed to be in units of radians.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gacc9b092df8257c68f19c9053703e2563"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gacc9b092df8257c68f19c9053703e2563"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00373.html#gacc9b092df8257c68f19c9053703e2563">acos</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gacc9b092df8257c68f19c9053703e2563"><td class="mdescLeft">&#160;</td><td class="mdescRight">Arc cosine.  <a href="a00373.html#gacc9b092df8257c68f19c9053703e2563">More...</a><br /></td></tr>
<tr class="separator:gacc9b092df8257c68f19c9053703e2563"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga858f35dc66fd2688f20c52b5f25be76a"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga858f35dc66fd2688f20c52b5f25be76a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00373.html#ga858f35dc66fd2688f20c52b5f25be76a">acosh</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga858f35dc66fd2688f20c52b5f25be76a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Arc hyperbolic cosine; returns the non-negative inverse of cosh.  <a href="a00373.html#ga858f35dc66fd2688f20c52b5f25be76a">More...</a><br /></td></tr>
<tr class="separator:ga858f35dc66fd2688f20c52b5f25be76a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0552d2df4865fa8c3d7cfc3ec2caac73"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga0552d2df4865fa8c3d7cfc3ec2caac73"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00373.html#ga0552d2df4865fa8c3d7cfc3ec2caac73">asin</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga0552d2df4865fa8c3d7cfc3ec2caac73"><td class="mdescLeft">&#160;</td><td class="mdescRight">Arc sine.  <a href="a00373.html#ga0552d2df4865fa8c3d7cfc3ec2caac73">More...</a><br /></td></tr>
<tr class="separator:ga0552d2df4865fa8c3d7cfc3ec2caac73"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3ef16b501ee859fddde88e22192a5950"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga3ef16b501ee859fddde88e22192a5950"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00373.html#ga3ef16b501ee859fddde88e22192a5950">asinh</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:ga3ef16b501ee859fddde88e22192a5950"><td class="mdescLeft">&#160;</td><td class="mdescRight">Arc hyperbolic sine; returns the inverse of sinh.  <a href="a00373.html#ga3ef16b501ee859fddde88e22192a5950">More...</a><br /></td></tr>
<tr class="separator:ga3ef16b501ee859fddde88e22192a5950"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac61629f3a4aa14057e7a8cae002291db"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gac61629f3a4aa14057e7a8cae002291db"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00373.html#gac61629f3a4aa14057e7a8cae002291db">atan</a> (vec&lt; L, T, Q &gt; const &amp;y, vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gac61629f3a4aa14057e7a8cae002291db"><td class="mdescLeft">&#160;</td><td class="mdescRight">Arc tangent.  <a href="a00373.html#gac61629f3a4aa14057e7a8cae002291db">More...</a><br /></td></tr>
<tr class="separator:gac61629f3a4aa14057e7a8cae002291db"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5229f087eaccbc466f1c609ce3107b95"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga5229f087eaccbc466f1c609ce3107b95"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00373.html#ga5229f087eaccbc466f1c609ce3107b95">atan</a> (vec&lt; L, T, Q &gt; const &amp;y_over_x)</td></tr>
<tr class="memdesc:ga5229f087eaccbc466f1c609ce3107b95"><td class="mdescLeft">&#160;</td><td class="mdescRight">Arc tangent.  <a href="a00373.html#ga5229f087eaccbc466f1c609ce3107b95">More...</a><br /></td></tr>
<tr class="separator:ga5229f087eaccbc466f1c609ce3107b95"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabc925650e618357d07da255531658b87"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gabc925650e618357d07da255531658b87"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00373.html#gabc925650e618357d07da255531658b87">atanh</a> (vec&lt; L, T, Q &gt; const &amp;x)</td></tr>
<tr class="memdesc:gabc925650e618357d07da255531658b87"><td class="mdescLeft">&#160;</td><td class="mdescRight">Arc hyperbolic tangent; returns the inverse of tanh.  <a href="a00373.html#gabc925650e618357d07da255531658b87">More...</a><br /></td></tr>
<tr class="separator:gabc925650e618357d07da255531658b87"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6a41efc740e3b3c937447d3a6284130e"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga6a41efc740e3b3c937447d3a6284130e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00373.html#ga6a41efc740e3b3c937447d3a6284130e">cos</a> (vec&lt; L, T, Q &gt; const &amp;angle)</td></tr>
<tr class="memdesc:ga6a41efc740e3b3c937447d3a6284130e"><td class="mdescLeft">&#160;</td><td class="mdescRight">The standard trigonometric cosine function.  <a href="a00373.html#ga6a41efc740e3b3c937447d3a6284130e">More...</a><br /></td></tr>
<tr class="separator:ga6a41efc740e3b3c937447d3a6284130e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4e260e372742c5f517aca196cf1e62b3"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga4e260e372742c5f517aca196cf1e62b3"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00373.html#ga4e260e372742c5f517aca196cf1e62b3">cosh</a> (vec&lt; L, T, Q &gt; const &amp;angle)</td></tr>
<tr class="memdesc:ga4e260e372742c5f517aca196cf1e62b3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the hyperbolic cosine function, (exp(x) + exp(-x)) / 2.  <a href="a00373.html#ga4e260e372742c5f517aca196cf1e62b3">More...</a><br /></td></tr>
<tr class="separator:ga4e260e372742c5f517aca196cf1e62b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8faec9e303538065911ba8b3caf7326b"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga8faec9e303538065911ba8b3caf7326b"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00373.html#ga8faec9e303538065911ba8b3caf7326b">degrees</a> (vec&lt; L, T, Q &gt; const &amp;radians)</td></tr>
<tr class="memdesc:ga8faec9e303538065911ba8b3caf7326b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts radians to degrees and returns the result.  <a href="a00373.html#ga8faec9e303538065911ba8b3caf7326b">More...</a><br /></td></tr>
<tr class="separator:ga8faec9e303538065911ba8b3caf7326b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6e1db4862c5e25afd553930e2fdd6a68"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga6e1db4862c5e25afd553930e2fdd6a68"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00373.html#ga6e1db4862c5e25afd553930e2fdd6a68">radians</a> (vec&lt; L, T, Q &gt; const &amp;degrees)</td></tr>
<tr class="memdesc:ga6e1db4862c5e25afd553930e2fdd6a68"><td class="mdescLeft">&#160;</td><td class="mdescRight">Converts degrees to radians and returns the result.  <a href="a00373.html#ga6e1db4862c5e25afd553930e2fdd6a68">More...</a><br /></td></tr>
<tr class="separator:ga6e1db4862c5e25afd553930e2fdd6a68"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga29747fd108cb7292ae5a284f69691a69"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga29747fd108cb7292ae5a284f69691a69"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00373.html#ga29747fd108cb7292ae5a284f69691a69">sin</a> (vec&lt; L, T, Q &gt; const &amp;angle)</td></tr>
<tr class="memdesc:ga29747fd108cb7292ae5a284f69691a69"><td class="mdescLeft">&#160;</td><td class="mdescRight">The standard trigonometric sine function.  <a href="a00373.html#ga29747fd108cb7292ae5a284f69691a69">More...</a><br /></td></tr>
<tr class="separator:ga29747fd108cb7292ae5a284f69691a69"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac7c39ff21809e281552b4dbe46f4a39d"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gac7c39ff21809e281552b4dbe46f4a39d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00373.html#gac7c39ff21809e281552b4dbe46f4a39d">sinh</a> (vec&lt; L, T, Q &gt; const &amp;angle)</td></tr>
<tr class="memdesc:gac7c39ff21809e281552b4dbe46f4a39d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the hyperbolic sine function, (exp(x) - exp(-x)) / 2.  <a href="a00373.html#gac7c39ff21809e281552b4dbe46f4a39d">More...</a><br /></td></tr>
<tr class="separator:gac7c39ff21809e281552b4dbe46f4a39d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga293a34cfb9f0115cc606b4a97c84f11f"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga293a34cfb9f0115cc606b4a97c84f11f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00373.html#ga293a34cfb9f0115cc606b4a97c84f11f">tan</a> (vec&lt; L, T, Q &gt; const &amp;angle)</td></tr>
<tr class="memdesc:ga293a34cfb9f0115cc606b4a97c84f11f"><td class="mdescLeft">&#160;</td><td class="mdescRight">The standard trigonometric tangent function.  <a href="a00373.html#ga293a34cfb9f0115cc606b4a97c84f11f">More...</a><br /></td></tr>
<tr class="separator:ga293a34cfb9f0115cc606b4a97c84f11f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa1bccbfdcbe40ed2ffcddc2aa8bfd0f1"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaa1bccbfdcbe40ed2ffcddc2aa8bfd0f1"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00373.html#gaa1bccbfdcbe40ed2ffcddc2aa8bfd0f1">tanh</a> (vec&lt; L, T, Q &gt; const &amp;angle)</td></tr>
<tr class="memdesc:gaa1bccbfdcbe40ed2ffcddc2aa8bfd0f1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the hyperbolic tangent function, sinh(angle) / cosh(angle)  <a href="a00373.html#gaa1bccbfdcbe40ed2ffcddc2aa8bfd0f1">More...</a><br /></td></tr>
<tr class="separator:gaa1bccbfdcbe40ed2ffcddc2aa8bfd0f1"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Function parameters specified as angle are assumed to be in units of radians. </p>
<p>In no case will any of these functions result in a divide by zero error. If the divisor of a ratio is 0, then results will be undefined.</p>
<p>These all operate component-wise. The description is per component.</p>
<p>Include &lt;<a class="el" href="a00160.html" title="Core features ">glm/trigonometric.hpp</a>&gt; to use these core features.</p>
<dl class="section see"><dt>See also</dt><dd>ext_vector_trigonometric </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gacc9b092df8257c68f19c9053703e2563"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::acos </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Arc cosine. </p>
<p>Returns an angle whose sine is x. The range of values returned by this function is [0, PI]. Results are undefined if |x| &gt; 1.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/acos.xml">GLSL acos man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.1 Angle and Trigonometry Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga858f35dc66fd2688f20c52b5f25be76a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::acosh </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Arc hyperbolic cosine; returns the non-negative inverse of cosh. </p>
<p>Results are undefined if x &lt; 1.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/acosh.xml">GLSL acosh man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.1 Angle and Trigonometry Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga0552d2df4865fa8c3d7cfc3ec2caac73"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::asin </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Arc sine. </p>
<p>Returns an angle whose sine is x. The range of values returned by this function is [-PI/2, PI/2]. Results are undefined if |x| &gt; 1.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/asin.xml">GLSL asin man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.1 Angle and Trigonometry Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga3ef16b501ee859fddde88e22192a5950"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::asinh </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Arc hyperbolic sine; returns the inverse of sinh. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/asinh.xml">GLSL asinh man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.1 Angle and Trigonometry Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gac61629f3a4aa14057e7a8cae002291db"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::atan </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Arc tangent. </p>
<p>Returns an angle whose tangent is y/x. The signs of x and y are used to determine what quadrant the angle is in. The range of values returned by this function is [-PI, PI]. Results are undefined if x and y are both 0.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/atan.xml">GLSL atan man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.1 Angle and Trigonometry Functions</a> </dd></dl>

<p>Referenced by <a class="el" href="a00017_source.html#l00055">glm::atan2()</a>.</p>

</div>
</div>
<a class="anchor" id="ga5229f087eaccbc466f1c609ce3107b95"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::atan </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y_over_x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Arc tangent. </p>
<p>Returns an angle whose tangent is y_over_x. The range of values returned by this function is [-PI/2, PI/2].</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/atan.xml">GLSL atan man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.1 Angle and Trigonometry Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gabc925650e618357d07da255531658b87"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::atanh </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Arc hyperbolic tangent; returns the inverse of tanh. </p>
<p>Results are undefined if abs(x) &gt;= 1.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/atanh.xml">GLSL atanh man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.1 Angle and Trigonometry Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga6a41efc740e3b3c937447d3a6284130e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::cos </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>angle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>The standard trigonometric cosine function. </p>
<p>The values returned by this function will range from [-1, 1].</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/cos.xml">GLSL cos man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.1 Angle and Trigonometry Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga4e260e372742c5f517aca196cf1e62b3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::cosh </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>angle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the hyperbolic cosine function, (exp(x) + exp(-x)) / 2. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/cosh.xml">GLSL cosh man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.1 Angle and Trigonometry Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8faec9e303538065911ba8b3caf7326b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, T, Q&gt; glm::degrees </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>radians</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Converts radians to degrees and returns the result. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/degrees.xml">GLSL degrees man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.1 Angle and Trigonometry Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga6e1db4862c5e25afd553930e2fdd6a68"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL GLM_CONSTEXPR vec&lt;L, T, Q&gt; glm::radians </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>degrees</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Converts degrees to radians and returns the result. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/radians.xml">GLSL radians man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.1 Angle and Trigonometry Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga29747fd108cb7292ae5a284f69691a69"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::sin </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>angle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>The standard trigonometric sine function. </p>
<p>The values returned by this function will range from [-1, 1].</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/sin.xml">GLSL sin man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.1 Angle and Trigonometry Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gac7c39ff21809e281552b4dbe46f4a39d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::sinh </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>angle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the hyperbolic sine function, (exp(x) - exp(-x)) / 2. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/sinh.xml">GLSL sinh man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.1 Angle and Trigonometry Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga293a34cfb9f0115cc606b4a97c84f11f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::tan </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>angle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>The standard trigonometric tangent function. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/tan.xml">GLSL tan man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.1 Angle and Trigonometry Functions</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaa1bccbfdcbe40ed2ffcddc2aa8bfd0f1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::tanh </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>angle</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the hyperbolic tangent function, sinh(angle) / cosh(angle) </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="http://www.opengl.org/sdk/docs/manglsl/xhtml/tanh.xml">GLSL tanh man page</a> </dd>
<dd>
<a href="http://www.opengl.org/registry/doc/GLSLangSpec.4.20.8.pdf">GLSL 4.20.8 specification, section 8.1 Angle and Trigonometry Functions</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
