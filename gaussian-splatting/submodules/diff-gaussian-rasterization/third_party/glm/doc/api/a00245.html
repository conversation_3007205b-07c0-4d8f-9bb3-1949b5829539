<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_EXT_matrix_projection</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_EXT_matrix_projection<div class="ingroups"><a class="el" href="a00285.html">Stable extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Functions that generate common projection transformation matrices.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gaf6b21eadb7ac2ecbbe258a9a233b4c82"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q, typename U &gt; </td></tr>
<tr class="memitem:gaf6b21eadb7ac2ecbbe258a9a233b4c82"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00245.html#gaf6b21eadb7ac2ecbbe258a9a233b4c82">pickMatrix</a> (vec&lt; 2, T, Q &gt; const &amp;center, vec&lt; 2, T, Q &gt; const &amp;delta, vec&lt; 4, U, Q &gt; const &amp;viewport)</td></tr>
<tr class="memdesc:gaf6b21eadb7ac2ecbbe258a9a233b4c82"><td class="mdescLeft">&#160;</td><td class="mdescRight">Define a picking region.  <a href="a00245.html#gaf6b21eadb7ac2ecbbe258a9a233b4c82">More...</a><br /></td></tr>
<tr class="separator:gaf6b21eadb7ac2ecbbe258a9a233b4c82"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf36e96033f456659e6705472a06b6e11"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf36e96033f456659e6705472a06b6e11"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00245.html#gaf36e96033f456659e6705472a06b6e11">project</a> (vec&lt; 3, T, Q &gt; const &amp;obj, mat&lt; 4, 4, T, Q &gt; const &amp;model, mat&lt; 4, 4, T, Q &gt; const &amp;proj, vec&lt; 4, U, Q &gt; const &amp;viewport)</td></tr>
<tr class="memdesc:gaf36e96033f456659e6705472a06b6e11"><td class="mdescLeft">&#160;</td><td class="mdescRight">Map the specified object coordinates (obj.x, obj.y, obj.z) into window coordinates using default near and far clip planes definition.  <a href="a00245.html#gaf36e96033f456659e6705472a06b6e11">More...</a><br /></td></tr>
<tr class="separator:gaf36e96033f456659e6705472a06b6e11"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga05249751f48d14cb282e4979802b8111"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:ga05249751f48d14cb282e4979802b8111"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00245.html#ga05249751f48d14cb282e4979802b8111">projectNO</a> (vec&lt; 3, T, Q &gt; const &amp;obj, mat&lt; 4, 4, T, Q &gt; const &amp;model, mat&lt; 4, 4, T, Q &gt; const &amp;proj, vec&lt; 4, U, Q &gt; const &amp;viewport)</td></tr>
<tr class="memdesc:ga05249751f48d14cb282e4979802b8111"><td class="mdescLeft">&#160;</td><td class="mdescRight">Map the specified object coordinates (obj.x, obj.y, obj.z) into window coordinates.  <a href="a00245.html#ga05249751f48d14cb282e4979802b8111">More...</a><br /></td></tr>
<tr class="separator:ga05249751f48d14cb282e4979802b8111"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga77d157525063dec83a557186873ee080"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:ga77d157525063dec83a557186873ee080"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00245.html#ga77d157525063dec83a557186873ee080">projectZO</a> (vec&lt; 3, T, Q &gt; const &amp;obj, mat&lt; 4, 4, T, Q &gt; const &amp;model, mat&lt; 4, 4, T, Q &gt; const &amp;proj, vec&lt; 4, U, Q &gt; const &amp;viewport)</td></tr>
<tr class="memdesc:ga77d157525063dec83a557186873ee080"><td class="mdescLeft">&#160;</td><td class="mdescRight">Map the specified object coordinates (obj.x, obj.y, obj.z) into window coordinates.  <a href="a00245.html#ga77d157525063dec83a557186873ee080">More...</a><br /></td></tr>
<tr class="separator:ga77d157525063dec83a557186873ee080"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga36641e5d60f994e01c3d8f56b10263d2"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:ga36641e5d60f994e01c3d8f56b10263d2"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00245.html#ga36641e5d60f994e01c3d8f56b10263d2">unProject</a> (vec&lt; 3, T, Q &gt; const &amp;win, mat&lt; 4, 4, T, Q &gt; const &amp;model, mat&lt; 4, 4, T, Q &gt; const &amp;proj, vec&lt; 4, U, Q &gt; const &amp;viewport)</td></tr>
<tr class="memdesc:ga36641e5d60f994e01c3d8f56b10263d2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Map the specified window coordinates (win.x, win.y, win.z) into object coordinates using default near and far clip planes definition.  <a href="a00245.html#ga36641e5d60f994e01c3d8f56b10263d2">More...</a><br /></td></tr>
<tr class="separator:ga36641e5d60f994e01c3d8f56b10263d2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae089ba9fc150ff69c252a20e508857b5"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:gae089ba9fc150ff69c252a20e508857b5"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00245.html#gae089ba9fc150ff69c252a20e508857b5">unProjectNO</a> (vec&lt; 3, T, Q &gt; const &amp;win, mat&lt; 4, 4, T, Q &gt; const &amp;model, mat&lt; 4, 4, T, Q &gt; const &amp;proj, vec&lt; 4, U, Q &gt; const &amp;viewport)</td></tr>
<tr class="memdesc:gae089ba9fc150ff69c252a20e508857b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Map the specified window coordinates (win.x, win.y, win.z) into object coordinates.  <a href="a00245.html#gae089ba9fc150ff69c252a20e508857b5">More...</a><br /></td></tr>
<tr class="separator:gae089ba9fc150ff69c252a20e508857b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gade5136413ce530f8e606124d570fba32"><td class="memTemplParams" colspan="2">template&lt;typename T , typename U , qualifier Q&gt; </td></tr>
<tr class="memitem:gade5136413ce530f8e606124d570fba32"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00245.html#gade5136413ce530f8e606124d570fba32">unProjectZO</a> (vec&lt; 3, T, Q &gt; const &amp;win, mat&lt; 4, 4, T, Q &gt; const &amp;model, mat&lt; 4, 4, T, Q &gt; const &amp;proj, vec&lt; 4, U, Q &gt; const &amp;viewport)</td></tr>
<tr class="memdesc:gade5136413ce530f8e606124d570fba32"><td class="mdescLeft">&#160;</td><td class="mdescRight">Map the specified window coordinates (win.x, win.y, win.z) into object coordinates.  <a href="a00245.html#gade5136413ce530f8e606124d570fba32">More...</a><br /></td></tr>
<tr class="separator:gade5136413ce530f8e606124d570fba32"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Functions that generate common projection transformation matrices. </p>
<p>The matrices generated by this extension use standard OpenGL fixed-function conventions. For example, the lookAt function generates a transform from world space into the specific eye space that the projective matrix functions (perspective, ortho, etc) are designed to expect. The OpenGL compatibility specifications defines the particular layout of this eye space.</p>
<p>Include &lt;<a class="el" href="a00105.html" title="GLM_EXT_matrix_projection ">glm/ext/matrix_projection.hpp</a>&gt; to use the features of this extension.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00247.html" title="Defines functions that generate common transformation matrices. ">GLM_EXT_matrix_transform</a> </dd>
<dd>
<a class="el" href="a00243.html" title="Defines functions that generate clip space transformation matrices. ">GLM_EXT_matrix_clip_space</a> </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="gaf6b21eadb7ac2ecbbe258a9a233b4c82"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::pickMatrix </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>center</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>delta</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 4, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>viewport</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Define a picking region. </p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">center</td><td>Specify the center of a picking region in window coordinates. </td></tr>
    <tr><td class="paramname">delta</td><td>Specify the width and height, respectively, of the picking region in window coordinates. </td></tr>
    <tr><td class="paramname">viewport</td><td>Rendering viewport </td></tr>
  </table>
  </dd>
</dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Native type used for the computation. Currently supported: half (not recommended), float or double. </td></tr>
    <tr><td class="paramname">U</td><td>Currently supported: Floating-point types and integer types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/gluPickMatrix.xml">gluPickMatrix man page</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaf36e96033f456659e6705472a06b6e11"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::project </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>obj</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>model</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>proj</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 4, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>viewport</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Map the specified object coordinates (obj.x, obj.y, obj.z) into window coordinates using default near and far clip planes definition. </p>
<p>To change default near and far clip planes definition use GLM_FORCE_DEPTH_ZERO_TO_ONE.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">obj</td><td>Specify the object coordinates. </td></tr>
    <tr><td class="paramname">model</td><td>Specifies the current modelview matrix </td></tr>
    <tr><td class="paramname">proj</td><td>Specifies the current projection matrix </td></tr>
    <tr><td class="paramname">viewport</td><td>Specifies the current viewport </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Return the computed window coordinates. </dd></dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Native type used for the computation. Currently supported: half (not recommended), float or double. </td></tr>
    <tr><td class="paramname">U</td><td>Currently supported: Floating-point types and integer types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/gluProject.xml">gluProject man page</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga05249751f48d14cb282e4979802b8111"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::projectNO </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>obj</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>model</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>proj</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 4, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>viewport</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Map the specified object coordinates (obj.x, obj.y, obj.z) into window coordinates. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">obj</td><td>Specify the object coordinates. </td></tr>
    <tr><td class="paramname">model</td><td>Specifies the current modelview matrix </td></tr>
    <tr><td class="paramname">proj</td><td>Specifies the current projection matrix </td></tr>
    <tr><td class="paramname">viewport</td><td>Specifies the current viewport </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Return the computed window coordinates. </dd></dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Native type used for the computation. Currently supported: half (not recommended), float or double. </td></tr>
    <tr><td class="paramname">U</td><td>Currently supported: Floating-point types and integer types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/gluProject.xml">gluProject man page</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga77d157525063dec83a557186873ee080"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::projectZO </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>obj</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>model</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>proj</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 4, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>viewport</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Map the specified object coordinates (obj.x, obj.y, obj.z) into window coordinates. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">obj</td><td>Specify the object coordinates. </td></tr>
    <tr><td class="paramname">model</td><td>Specifies the current modelview matrix </td></tr>
    <tr><td class="paramname">proj</td><td>Specifies the current projection matrix </td></tr>
    <tr><td class="paramname">viewport</td><td>Specifies the current viewport </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Return the computed window coordinates. </dd></dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Native type used for the computation. Currently supported: half (not recommended), float or double. </td></tr>
    <tr><td class="paramname">U</td><td>Currently supported: Floating-point types and integer types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/gluProject.xml">gluProject man page</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga36641e5d60f994e01c3d8f56b10263d2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::unProject </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>win</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>model</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>proj</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 4, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>viewport</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Map the specified window coordinates (win.x, win.y, win.z) into object coordinates using default near and far clip planes definition. </p>
<p>To change default near and far clip planes definition use GLM_FORCE_DEPTH_ZERO_TO_ONE.</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">win</td><td>Specify the window coordinates to be mapped. </td></tr>
    <tr><td class="paramname">model</td><td>Specifies the modelview matrix </td></tr>
    <tr><td class="paramname">proj</td><td>Specifies the projection matrix </td></tr>
    <tr><td class="paramname">viewport</td><td>Specifies the viewport </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Returns the computed object coordinates. </dd></dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Native type used for the computation. Currently supported: half (not recommended), float or double. </td></tr>
    <tr><td class="paramname">U</td><td>Currently supported: Floating-point types and integer types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/gluUnProject.xml">gluUnProject man page</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae089ba9fc150ff69c252a20e508857b5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::unProjectNO </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>win</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>model</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>proj</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 4, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>viewport</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Map the specified window coordinates (win.x, win.y, win.z) into object coordinates. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of -1 and +1 respectively. (OpenGL clip volume definition)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">win</td><td>Specify the window coordinates to be mapped. </td></tr>
    <tr><td class="paramname">model</td><td>Specifies the modelview matrix </td></tr>
    <tr><td class="paramname">proj</td><td>Specifies the projection matrix </td></tr>
    <tr><td class="paramname">viewport</td><td>Specifies the viewport </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Returns the computed object coordinates. </dd></dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Native type used for the computation. Currently supported: half (not recommended), float or double. </td></tr>
    <tr><td class="paramname">U</td><td>Currently supported: Floating-point types and integer types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/gluUnProject.xml">gluUnProject man page</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gade5136413ce530f8e606124d570fba32"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;3, T, Q&gt; glm::unProjectZO </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>win</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>model</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>proj</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; 4, U, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>viewport</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Map the specified window coordinates (win.x, win.y, win.z) into object coordinates. </p>
<p>The near and far clip planes correspond to z normalized device coordinates of 0 and +1 respectively. (Direct3D clip volume definition)</p>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">win</td><td>Specify the window coordinates to be mapped. </td></tr>
    <tr><td class="paramname">model</td><td>Specifies the modelview matrix </td></tr>
    <tr><td class="paramname">proj</td><td>Specifies the projection matrix </td></tr>
    <tr><td class="paramname">viewport</td><td>Specifies the viewport </td></tr>
  </table>
  </dd>
</dl>
<dl class="section return"><dt>Returns</dt><dd>Returns the computed object coordinates. </dd></dl>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Native type used for the computation. Currently supported: half (not recommended), float or double. </td></tr>
    <tr><td class="paramname">U</td><td>Currently supported: Floating-point types and integer types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a href="https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/gluUnProject.xml">gluUnProject man page</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
