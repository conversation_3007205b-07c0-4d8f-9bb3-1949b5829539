<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_std_based_type</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_std_based_type<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00155.html" title="GLM_GTX_std_based_type ">glm/gtx/std_based_type.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:gaeb877ac8f9a3703961736c1c5072cf68"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 1, std::size_t, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00359.html#gaeb877ac8f9a3703961736c1c5072cf68">size1</a></td></tr>
<tr class="memdesc:gaeb877ac8f9a3703961736c1c5072cf68"><td class="mdescLeft">&#160;</td><td class="mdescRight">Vector type based of one std::size_t component.  <a href="a00359.html#gaeb877ac8f9a3703961736c1c5072cf68">More...</a><br /></td></tr>
<tr class="separator:gaeb877ac8f9a3703961736c1c5072cf68"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaaf6accc57f5aa50447ba7310ce3f0d6f"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 1, std::size_t, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00359.html#gaaf6accc57f5aa50447ba7310ce3f0d6f">size1_t</a></td></tr>
<tr class="memdesc:gaaf6accc57f5aa50447ba7310ce3f0d6f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Vector type based of one std::size_t component.  <a href="a00359.html#gaaf6accc57f5aa50447ba7310ce3f0d6f">More...</a><br /></td></tr>
<tr class="separator:gaaf6accc57f5aa50447ba7310ce3f0d6f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1bfe8c4975ff282bce41be2bacd524fe"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, std::size_t, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00359.html#ga1bfe8c4975ff282bce41be2bacd524fe">size2</a></td></tr>
<tr class="memdesc:ga1bfe8c4975ff282bce41be2bacd524fe"><td class="mdescLeft">&#160;</td><td class="mdescRight">Vector type based of two std::size_t components.  <a href="a00359.html#ga1bfe8c4975ff282bce41be2bacd524fe">More...</a><br /></td></tr>
<tr class="separator:ga1bfe8c4975ff282bce41be2bacd524fe"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5976c25657d4e2b5f73f39364c3845d6"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 2, std::size_t, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00359.html#ga5976c25657d4e2b5f73f39364c3845d6">size2_t</a></td></tr>
<tr class="memdesc:ga5976c25657d4e2b5f73f39364c3845d6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Vector type based of two std::size_t components.  <a href="a00359.html#ga5976c25657d4e2b5f73f39364c3845d6">More...</a><br /></td></tr>
<tr class="separator:ga5976c25657d4e2b5f73f39364c3845d6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae1c72956d0359b0db332c6c8774d3b04"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, std::size_t, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00359.html#gae1c72956d0359b0db332c6c8774d3b04">size3</a></td></tr>
<tr class="memdesc:gae1c72956d0359b0db332c6c8774d3b04"><td class="mdescLeft">&#160;</td><td class="mdescRight">Vector type based of three std::size_t components.  <a href="a00359.html#gae1c72956d0359b0db332c6c8774d3b04">More...</a><br /></td></tr>
<tr class="separator:gae1c72956d0359b0db332c6c8774d3b04"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf2654983c60d641fd3808e65a8dfad8d"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 3, std::size_t, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00359.html#gaf2654983c60d641fd3808e65a8dfad8d">size3_t</a></td></tr>
<tr class="memdesc:gaf2654983c60d641fd3808e65a8dfad8d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Vector type based of three std::size_t components.  <a href="a00359.html#gaf2654983c60d641fd3808e65a8dfad8d">More...</a><br /></td></tr>
<tr class="separator:gaf2654983c60d641fd3808e65a8dfad8d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3a19dde617beaf8ce3cfc2ac5064e9aa"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, std::size_t, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00359.html#ga3a19dde617beaf8ce3cfc2ac5064e9aa">size4</a></td></tr>
<tr class="memdesc:ga3a19dde617beaf8ce3cfc2ac5064e9aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Vector type based of four std::size_t components.  <a href="a00359.html#ga3a19dde617beaf8ce3cfc2ac5064e9aa">More...</a><br /></td></tr>
<tr class="separator:ga3a19dde617beaf8ce3cfc2ac5064e9aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa423efcea63675a2df26990dbcb58656"><td class="memItemLeft" align="right" valign="top">typedef vec&lt; 4, std::size_t, defaultp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00359.html#gaa423efcea63675a2df26990dbcb58656">size4_t</a></td></tr>
<tr class="memdesc:gaa423efcea63675a2df26990dbcb58656"><td class="mdescLeft">&#160;</td><td class="mdescRight">Vector type based of four std::size_t components.  <a href="a00359.html#gaa423efcea63675a2df26990dbcb58656">More...</a><br /></td></tr>
<tr class="separator:gaa423efcea63675a2df26990dbcb58656"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00155.html" title="GLM_GTX_std_based_type ">glm/gtx/std_based_type.hpp</a>&gt; to use the features of this extension. </p>
<p>Adds vector types based on STL value types. </p>
<h2 class="groupheader">Typedef Documentation</h2>
<a class="anchor" id="gaeb877ac8f9a3703961736c1c5072cf68"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt;1, std::size_t, defaultp&gt; size1</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Vector type based of one std::size_t component. </p>
<dl class="section see"><dt>See also</dt><dd>GLM_GTX_std_based_type </dd></dl>

<p>Definition at line <a class="el" href="a00155_source.html#l00035">35</a> of file <a class="el" href="a00155_source.html">std_based_type.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaaf6accc57f5aa50447ba7310ce3f0d6f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt;1, std::size_t, defaultp&gt; size1_t</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Vector type based of one std::size_t component. </p>
<dl class="section see"><dt>See also</dt><dd>GLM_GTX_std_based_type </dd></dl>

<p>Definition at line <a class="el" href="a00155_source.html#l00051">51</a> of file <a class="el" href="a00155_source.html">std_based_type.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga1bfe8c4975ff282bce41be2bacd524fe"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt;2, std::size_t, defaultp&gt; size2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Vector type based of two std::size_t components. </p>
<dl class="section see"><dt>See also</dt><dd>GLM_GTX_std_based_type </dd></dl>

<p>Definition at line <a class="el" href="a00155_source.html#l00039">39</a> of file <a class="el" href="a00155_source.html">std_based_type.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga5976c25657d4e2b5f73f39364c3845d6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt;2, std::size_t, defaultp&gt; size2_t</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Vector type based of two std::size_t components. </p>
<dl class="section see"><dt>See also</dt><dd>GLM_GTX_std_based_type </dd></dl>

<p>Definition at line <a class="el" href="a00155_source.html#l00055">55</a> of file <a class="el" href="a00155_source.html">std_based_type.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gae1c72956d0359b0db332c6c8774d3b04"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt;3, std::size_t, defaultp&gt; size3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Vector type based of three std::size_t components. </p>
<dl class="section see"><dt>See also</dt><dd>GLM_GTX_std_based_type </dd></dl>

<p>Definition at line <a class="el" href="a00155_source.html#l00043">43</a> of file <a class="el" href="a00155_source.html">std_based_type.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaf2654983c60d641fd3808e65a8dfad8d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt;3, std::size_t, defaultp&gt; size3_t</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Vector type based of three std::size_t components. </p>
<dl class="section see"><dt>See also</dt><dd>GLM_GTX_std_based_type </dd></dl>

<p>Definition at line <a class="el" href="a00155_source.html#l00059">59</a> of file <a class="el" href="a00155_source.html">std_based_type.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga3a19dde617beaf8ce3cfc2ac5064e9aa"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt;4, std::size_t, defaultp&gt; size4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Vector type based of four std::size_t components. </p>
<dl class="section see"><dt>See also</dt><dd>GLM_GTX_std_based_type </dd></dl>

<p>Definition at line <a class="el" href="a00155_source.html#l00047">47</a> of file <a class="el" href="a00155_source.html">std_based_type.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaa423efcea63675a2df26990dbcb58656"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef vec&lt;4, std::size_t, defaultp&gt; size4_t</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Vector type based of four std::size_t components. </p>
<dl class="section see"><dt>See also</dt><dd>GLM_GTX_std_based_type </dd></dl>

<p>Definition at line <a class="el" href="a00155_source.html#l00063">63</a> of file <a class="el" href="a00155_source.html">std_based_type.hpp</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
