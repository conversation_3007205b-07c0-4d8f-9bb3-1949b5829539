<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTC_color_space</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTC_color_space<div class="ingroups"><a class="el" href="a00286.html">Recommended extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00012.html" title="GLM_GTC_color_space ">glm/gtc/color_space.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga42239e7b3da900f7ef37cec7e2476579"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga42239e7b3da900f7ef37cec7e2476579"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00289.html#ga42239e7b3da900f7ef37cec7e2476579">convertLinearToSRGB</a> (vec&lt; L, T, Q &gt; const &amp;ColorLinear)</td></tr>
<tr class="memdesc:ga42239e7b3da900f7ef37cec7e2476579"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a linear color to sRGB color using a standard gamma correction.  <a href="a00289.html#ga42239e7b3da900f7ef37cec7e2476579">More...</a><br /></td></tr>
<tr class="separator:ga42239e7b3da900f7ef37cec7e2476579"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaace0a21167d13d26116c283009af57f6"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaace0a21167d13d26116c283009af57f6"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00289.html#gaace0a21167d13d26116c283009af57f6">convertLinearToSRGB</a> (vec&lt; L, T, Q &gt; const &amp;ColorLinear, T Gamma)</td></tr>
<tr class="memdesc:gaace0a21167d13d26116c283009af57f6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a linear color to sRGB color using a custom gamma correction.  <a href="a00289.html#gaace0a21167d13d26116c283009af57f6">More...</a><br /></td></tr>
<tr class="separator:gaace0a21167d13d26116c283009af57f6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga16c798b7a226b2c3079dedc55083d187"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga16c798b7a226b2c3079dedc55083d187"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00289.html#ga16c798b7a226b2c3079dedc55083d187">convertSRGBToLinear</a> (vec&lt; L, T, Q &gt; const &amp;ColorSRGB)</td></tr>
<tr class="memdesc:ga16c798b7a226b2c3079dedc55083d187"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a sRGB color to linear color using a standard gamma correction.  <a href="a00289.html#ga16c798b7a226b2c3079dedc55083d187">More...</a><br /></td></tr>
<tr class="separator:ga16c798b7a226b2c3079dedc55083d187"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad1b91f27a9726c9cb403f9fee6e2e200"><td class="memTemplParams" colspan="2"><a class="anchor" id="gad1b91f27a9726c9cb403f9fee6e2e200"></a>
template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gad1b91f27a9726c9cb403f9fee6e2e200"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00289.html#gad1b91f27a9726c9cb403f9fee6e2e200">convertSRGBToLinear</a> (vec&lt; L, T, Q &gt; const &amp;ColorSRGB, T Gamma)</td></tr>
<tr class="memdesc:gad1b91f27a9726c9cb403f9fee6e2e200"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a sRGB color to linear color using a custom gamma correction. <br /></td></tr>
<tr class="separator:gad1b91f27a9726c9cb403f9fee6e2e200"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00012.html" title="GLM_GTC_color_space ">glm/gtc/color_space.hpp</a>&gt; to use the features of this extension. </p>
<p>Allow to perform bit operations on integer values </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga42239e7b3da900f7ef37cec7e2476579"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::convertLinearToSRGB </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>ColorLinear</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a linear color to sRGB color using a standard gamma correction. </p>
<p>IEC 61966-2-1:1999 / Rec. 709 specification <a href="https://www.w3.org/Graphics/Color/srgb">https://www.w3.org/Graphics/Color/srgb</a> </p>

</div>
</div>
<a class="anchor" id="gaace0a21167d13d26116c283009af57f6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::convertLinearToSRGB </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>ColorLinear</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>Gamma</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a linear color to sRGB color using a custom gamma correction. </p>
<p>IEC 61966-2-1:1999 / Rec. 709 specification <a href="https://www.w3.org/Graphics/Color/srgb">https://www.w3.org/Graphics/Color/srgb</a> </p>

</div>
</div>
<a class="anchor" id="ga16c798b7a226b2c3079dedc55083d187"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::convertSRGBToLinear </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>ColorSRGB</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Convert a sRGB color to linear color using a standard gamma correction. </p>
<p>IEC 61966-2-1:1999 / Rec. 709 specification <a href="https://www.w3.org/Graphics/Color/srgb">https://www.w3.org/Graphics/Color/srgb</a> </p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
