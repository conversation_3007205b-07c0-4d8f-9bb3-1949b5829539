<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_matrix_operation</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_matrix_operation<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00104.html" title="GLM_GTX_matrix_operation ">glm/gtx/matrix_operation.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga40a38402a30860af6e508fe76211e659"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga40a38402a30860af6e508fe76211e659"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 2, 2, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00339.html#ga40a38402a30860af6e508fe76211e659">adjugate</a> (mat&lt; 2, 2, T, Q &gt; const &amp;m)</td></tr>
<tr class="memdesc:ga40a38402a30860af6e508fe76211e659"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build an adjugate matrix.  <a href="a00339.html#ga40a38402a30860af6e508fe76211e659">More...</a><br /></td></tr>
<tr class="separator:ga40a38402a30860af6e508fe76211e659"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaddb09f7abc1a9c56a243d32ff3538be6"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaddb09f7abc1a9c56a243d32ff3538be6"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00339.html#gaddb09f7abc1a9c56a243d32ff3538be6">adjugate</a> (mat&lt; 3, 3, T, Q &gt; const &amp;m)</td></tr>
<tr class="memdesc:gaddb09f7abc1a9c56a243d32ff3538be6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build an adjugate matrix.  <a href="a00339.html#gaddb09f7abc1a9c56a243d32ff3538be6">More...</a><br /></td></tr>
<tr class="separator:gaddb09f7abc1a9c56a243d32ff3538be6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9aaa7d1f40391b0b5cacccb60e104ba8"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga9aaa7d1f40391b0b5cacccb60e104ba8"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00339.html#ga9aaa7d1f40391b0b5cacccb60e104ba8">adjugate</a> (mat&lt; 4, 4, T, Q &gt; const &amp;m)</td></tr>
<tr class="memdesc:ga9aaa7d1f40391b0b5cacccb60e104ba8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build an adjugate matrix.  <a href="a00339.html#ga9aaa7d1f40391b0b5cacccb60e104ba8">More...</a><br /></td></tr>
<tr class="separator:ga9aaa7d1f40391b0b5cacccb60e104ba8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga58a32a2beeb2478dae2a721368cdd4ac"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga58a32a2beeb2478dae2a721368cdd4ac"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 2, 2, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00339.html#ga58a32a2beeb2478dae2a721368cdd4ac">diagonal2x2</a> (vec&lt; 2, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga58a32a2beeb2478dae2a721368cdd4ac"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a diagonal matrix.  <a href="a00339.html#ga58a32a2beeb2478dae2a721368cdd4ac">More...</a><br /></td></tr>
<tr class="separator:ga58a32a2beeb2478dae2a721368cdd4ac"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab69f900206a430e2875a5a073851e175"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gab69f900206a430e2875a5a073851e175"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 2, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00339.html#gab69f900206a430e2875a5a073851e175">diagonal2x3</a> (vec&lt; 2, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gab69f900206a430e2875a5a073851e175"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a diagonal matrix.  <a href="a00339.html#gab69f900206a430e2875a5a073851e175">More...</a><br /></td></tr>
<tr class="separator:gab69f900206a430e2875a5a073851e175"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga30b4dbfed60a919d66acc8a63bcdc549"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga30b4dbfed60a919d66acc8a63bcdc549"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 2, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00339.html#ga30b4dbfed60a919d66acc8a63bcdc549">diagonal2x4</a> (vec&lt; 2, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga30b4dbfed60a919d66acc8a63bcdc549"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a diagonal matrix.  <a href="a00339.html#ga30b4dbfed60a919d66acc8a63bcdc549">More...</a><br /></td></tr>
<tr class="separator:ga30b4dbfed60a919d66acc8a63bcdc549"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga832c805d5130d28ad76236958d15b47d"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga832c805d5130d28ad76236958d15b47d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 2, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00339.html#ga832c805d5130d28ad76236958d15b47d">diagonal3x2</a> (vec&lt; 2, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga832c805d5130d28ad76236958d15b47d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a diagonal matrix.  <a href="a00339.html#ga832c805d5130d28ad76236958d15b47d">More...</a><br /></td></tr>
<tr class="separator:ga832c805d5130d28ad76236958d15b47d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5487ff9cdbc8e04d594adef1bcb16ee0"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga5487ff9cdbc8e04d594adef1bcb16ee0"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00339.html#ga5487ff9cdbc8e04d594adef1bcb16ee0">diagonal3x3</a> (vec&lt; 3, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga5487ff9cdbc8e04d594adef1bcb16ee0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a diagonal matrix.  <a href="a00339.html#ga5487ff9cdbc8e04d594adef1bcb16ee0">More...</a><br /></td></tr>
<tr class="separator:ga5487ff9cdbc8e04d594adef1bcb16ee0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad7551139cff0c4208d27f0ad3437833e"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gad7551139cff0c4208d27f0ad3437833e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 3, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00339.html#gad7551139cff0c4208d27f0ad3437833e">diagonal3x4</a> (vec&lt; 3, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gad7551139cff0c4208d27f0ad3437833e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a diagonal matrix.  <a href="a00339.html#gad7551139cff0c4208d27f0ad3437833e">More...</a><br /></td></tr>
<tr class="separator:gad7551139cff0c4208d27f0ad3437833e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gacb8969e6543ba775c6638161a37ac330"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gacb8969e6543ba775c6638161a37ac330"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 2, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00339.html#gacb8969e6543ba775c6638161a37ac330">diagonal4x2</a> (vec&lt; 2, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gacb8969e6543ba775c6638161a37ac330"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a diagonal matrix.  <a href="a00339.html#gacb8969e6543ba775c6638161a37ac330">More...</a><br /></td></tr>
<tr class="separator:gacb8969e6543ba775c6638161a37ac330"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae235def5049d6740f0028433f5e13f90"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gae235def5049d6740f0028433f5e13f90"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00339.html#gae235def5049d6740f0028433f5e13f90">diagonal4x3</a> (vec&lt; 3, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:gae235def5049d6740f0028433f5e13f90"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a diagonal matrix.  <a href="a00339.html#gae235def5049d6740f0028433f5e13f90">More...</a><br /></td></tr>
<tr class="separator:gae235def5049d6740f0028433f5e13f90"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0b4cd8dea436791b072356231ee8578f"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga0b4cd8dea436791b072356231ee8578f"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL mat&lt; 4, 4, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00339.html#ga0b4cd8dea436791b072356231ee8578f">diagonal4x4</a> (vec&lt; 4, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga0b4cd8dea436791b072356231ee8578f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a diagonal matrix.  <a href="a00339.html#ga0b4cd8dea436791b072356231ee8578f">More...</a><br /></td></tr>
<tr class="separator:ga0b4cd8dea436791b072356231ee8578f"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00104.html" title="GLM_GTX_matrix_operation ">glm/gtx/matrix_operation.hpp</a>&gt; to use the features of this extension. </p>
<p>Build diagonal matrices from vectors. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga40a38402a30860af6e508fe76211e659"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;2, 2, T, Q&gt; glm::adjugate </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 2, 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build an adjugate matrix. </p>
<p>From GLM_GTX_matrix_operation extension. </p>

</div>
</div>
<a class="anchor" id="gaddb09f7abc1a9c56a243d32ff3538be6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;3, 3, T, Q&gt; glm::adjugate </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 3, 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build an adjugate matrix. </p>
<p>From GLM_GTX_matrix_operation extension. </p>

</div>
</div>
<a class="anchor" id="ga9aaa7d1f40391b0b5cacccb60e104ba8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::adjugate </td>
          <td>(</td>
          <td class="paramtype">mat&lt; 4, 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>m</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build an adjugate matrix. </p>
<p>From GLM_GTX_matrix_operation extension. </p>

</div>
</div>
<a class="anchor" id="ga58a32a2beeb2478dae2a721368cdd4ac"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;2, 2, T, Q&gt; glm::diagonal2x2 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a diagonal matrix. </p>
<p>From GLM_GTX_matrix_operation extension. </p>

</div>
</div>
<a class="anchor" id="gab69f900206a430e2875a5a073851e175"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;2, 3, T, Q&gt; glm::diagonal2x3 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a diagonal matrix. </p>
<p>From GLM_GTX_matrix_operation extension. </p>

</div>
</div>
<a class="anchor" id="ga30b4dbfed60a919d66acc8a63bcdc549"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;2, 4, T, Q&gt; glm::diagonal2x4 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a diagonal matrix. </p>
<p>From GLM_GTX_matrix_operation extension. </p>

</div>
</div>
<a class="anchor" id="ga832c805d5130d28ad76236958d15b47d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;3, 2, T, Q&gt; glm::diagonal3x2 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a diagonal matrix. </p>
<p>From GLM_GTX_matrix_operation extension. </p>

</div>
</div>
<a class="anchor" id="ga5487ff9cdbc8e04d594adef1bcb16ee0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;3, 3, T, Q&gt; glm::diagonal3x3 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a diagonal matrix. </p>
<p>From GLM_GTX_matrix_operation extension. </p>

</div>
</div>
<a class="anchor" id="gad7551139cff0c4208d27f0ad3437833e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;3, 4, T, Q&gt; glm::diagonal3x4 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a diagonal matrix. </p>
<p>From GLM_GTX_matrix_operation extension. </p>

</div>
</div>
<a class="anchor" id="gacb8969e6543ba775c6638161a37ac330"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 2, T, Q&gt; glm::diagonal4x2 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 2, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a diagonal matrix. </p>
<p>From GLM_GTX_matrix_operation extension. </p>

</div>
</div>
<a class="anchor" id="gae235def5049d6740f0028433f5e13f90"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 3, T, Q&gt; glm::diagonal4x3 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 3, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a diagonal matrix. </p>
<p>From GLM_GTX_matrix_operation extension. </p>

</div>
</div>
<a class="anchor" id="ga0b4cd8dea436791b072356231ee8578f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL mat&lt;4, 4, T, Q&gt; glm::diagonal4x4 </td>
          <td>(</td>
          <td class="paramtype">vec&lt; 4, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a diagonal matrix. </p>
<p>From GLM_GTX_matrix_operation extension. </p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
