<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_color_encoding</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_color_encoding<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00011.html" title="GLM_GTX_color_encoding ">glm/gtx/color_encoding.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:gad12f4f65022b2c80e33fcba2ced0dc48"><td class="memTemplParams" colspan="2"><a class="anchor" id="gad12f4f65022b2c80e33fcba2ced0dc48"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gad12f4f65022b2c80e33fcba2ced0dc48"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00311.html#gad12f4f65022b2c80e33fcba2ced0dc48">convertD65XYZToD50XYZ</a> (vec&lt; 3, T, Q &gt; const &amp;ColorD65XYZ)</td></tr>
<tr class="memdesc:gad12f4f65022b2c80e33fcba2ced0dc48"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a D65 YUV color to D50 YUV. <br /></td></tr>
<tr class="separator:gad12f4f65022b2c80e33fcba2ced0dc48"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5265386fc3ac29e4c580d37ed470859c"><td class="memTemplParams" colspan="2"><a class="anchor" id="ga5265386fc3ac29e4c580d37ed470859c"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga5265386fc3ac29e4c580d37ed470859c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00311.html#ga5265386fc3ac29e4c580d37ed470859c">convertD65XYZToLinearSRGB</a> (vec&lt; 3, T, Q &gt; const &amp;ColorD65XYZ)</td></tr>
<tr class="memdesc:ga5265386fc3ac29e4c580d37ed470859c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a D65 YUV color to linear sRGB. <br /></td></tr>
<tr class="separator:ga5265386fc3ac29e4c580d37ed470859c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1522ba180e3d83d554a734056da031f9"><td class="memTemplParams" colspan="2"><a class="anchor" id="ga1522ba180e3d83d554a734056da031f9"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga1522ba180e3d83d554a734056da031f9"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00311.html#ga1522ba180e3d83d554a734056da031f9">convertLinearSRGBToD50XYZ</a> (vec&lt; 3, T, Q &gt; const &amp;ColorLinearSRGB)</td></tr>
<tr class="memdesc:ga1522ba180e3d83d554a734056da031f9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a linear sRGB color to D50 YUV. <br /></td></tr>
<tr class="separator:ga1522ba180e3d83d554a734056da031f9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf9e130d9d4ccf51cc99317de7449f369"><td class="memTemplParams" colspan="2"><a class="anchor" id="gaf9e130d9d4ccf51cc99317de7449f369"></a>
template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gaf9e130d9d4ccf51cc99317de7449f369"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; 3, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00311.html#gaf9e130d9d4ccf51cc99317de7449f369">convertLinearSRGBToD65XYZ</a> (vec&lt; 3, T, Q &gt; const &amp;ColorLinearSRGB)</td></tr>
<tr class="memdesc:gaf9e130d9d4ccf51cc99317de7449f369"><td class="mdescLeft">&#160;</td><td class="mdescRight">Convert a linear sRGB color to D65 YUV. <br /></td></tr>
<tr class="separator:gaf9e130d9d4ccf51cc99317de7449f369"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00011.html" title="GLM_GTX_color_encoding ">glm/gtx/color_encoding.hpp</a>&gt; to use the features of this extension. </p>
<p>Allow to perform bit operations on integer values </p>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
