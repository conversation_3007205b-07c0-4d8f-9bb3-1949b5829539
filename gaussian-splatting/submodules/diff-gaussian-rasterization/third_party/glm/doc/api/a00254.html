<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_EXT_quaternion_geometric</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_EXT_quaternion_geometric<div class="ingroups"><a class="el" href="a00285.html">Stable extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Provides geometric functions for quaternion types.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga755beaa929c75751dee646cccba37e4c"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga755beaa929c75751dee646cccba37e4c"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_QUALIFIER qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00254.html#ga755beaa929c75751dee646cccba37e4c">cross</a> (qua&lt; T, Q &gt; const &amp;q1, qua&lt; T, Q &gt; const &amp;q2)</td></tr>
<tr class="memdesc:ga755beaa929c75751dee646cccba37e4c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Compute a cross product.  <a href="a00254.html#ga755beaa929c75751dee646cccba37e4c">More...</a><br /></td></tr>
<tr class="separator:ga755beaa929c75751dee646cccba37e4c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga84865a56acb8fbd7bc4f5c0b928e3cfc"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga84865a56acb8fbd7bc4f5c0b928e3cfc"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00254.html#ga84865a56acb8fbd7bc4f5c0b928e3cfc">dot</a> (qua&lt; T, Q &gt; const &amp;x, qua&lt; T, Q &gt; const &amp;y)</td></tr>
<tr class="memdesc:ga84865a56acb8fbd7bc4f5c0b928e3cfc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns dot product of q1 and q2, i.e., q1[0] * q2[0] + q1[1] * q2[1] + ...  <a href="a00254.html#ga84865a56acb8fbd7bc4f5c0b928e3cfc">More...</a><br /></td></tr>
<tr class="separator:ga84865a56acb8fbd7bc4f5c0b928e3cfc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab703732449be6c7199369b3f9a91ed38"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gab703732449be6c7199369b3f9a91ed38"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00254.html#gab703732449be6c7199369b3f9a91ed38">length</a> (qua&lt; T, Q &gt; const &amp;q)</td></tr>
<tr class="memdesc:gab703732449be6c7199369b3f9a91ed38"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the norm of a quaternions.  <a href="a00254.html#gab703732449be6c7199369b3f9a91ed38">More...</a><br /></td></tr>
<tr class="separator:gab703732449be6c7199369b3f9a91ed38"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabf30e3263fffe8dcc6659aea76ae8927"><td class="memTemplParams" colspan="2">template&lt;typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gabf30e3263fffe8dcc6659aea76ae8927"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL qua&lt; T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00254.html#gabf30e3263fffe8dcc6659aea76ae8927">normalize</a> (qua&lt; T, Q &gt; const &amp;q)</td></tr>
<tr class="memdesc:gabf30e3263fffe8dcc6659aea76ae8927"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the normalized quaternion.  <a href="a00254.html#gabf30e3263fffe8dcc6659aea76ae8927">More...</a><br /></td></tr>
<tr class="separator:gabf30e3263fffe8dcc6659aea76ae8927"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Provides geometric functions for quaternion types. </p>
<p>Include &lt;<a class="el" href="a00133.html" title="GLM_EXT_quaternion_geometric ">glm/ext/quaternion_geometric.hpp</a>&gt; to use the features of this extension.</p>
<dl class="section see"><dt>See also</dt><dd>core_geometric </dd>
<dd>
<a class="el" href="a00252.html" title="Exposes single-precision floating point quaternion type. ">GLM_EXT_quaternion_float</a> </dd>
<dd>
<a class="el" href="a00249.html" title="Exposes double-precision floating point quaternion type. ">GLM_EXT_quaternion_double</a> </dd></dl>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga755beaa929c75751dee646cccba37e4c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_QUALIFIER qua&lt;T, Q&gt; glm::cross </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q1</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q2</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Compute a cross product. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00254.html" title="Provides geometric functions for quaternion types. ">GLM_EXT_quaternion_geometric</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga84865a56acb8fbd7bc4f5c0b928e3cfc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::dot </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns dot product of q1 and q2, i.e., q1[0] * q2[0] + q1[1] * q2[1] + ... </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types. </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00254.html" title="Provides geometric functions for quaternion types. ">GLM_EXT_quaternion_geometric</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab703732449be6c7199369b3f9a91ed38"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::length </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the norm of a quaternions. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00254.html" title="Provides geometric functions for quaternion types. ">GLM_EXT_quaternion_geometric</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gabf30e3263fffe8dcc6659aea76ae8927"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL qua&lt;T, Q&gt; glm::normalize </td>
          <td>(</td>
          <td class="paramtype">qua&lt; T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>q</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the normalized quaternion. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00254.html" title="Provides geometric functions for quaternion types. ">GLM_EXT_quaternion_geometric</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
