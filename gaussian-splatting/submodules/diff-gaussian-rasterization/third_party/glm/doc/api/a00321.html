<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTX_extented_min_max</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTX_extented_min_max<div class="ingroups"><a class="el" href="a00287.html">Experimental extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;glm/gtx/extented_min_max.hpp&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga1e28539d3a46965ed9ef92ec7cb3b18a"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:ga1e28539d3a46965ed9ef92ec7cb3b18a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00321.html#ga1e28539d3a46965ed9ef92ec7cb3b18a">fclamp</a> (genType x, genType minVal, genType maxVal)</td></tr>
<tr class="memdesc:ga1e28539d3a46965ed9ef92ec7cb3b18a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns min(max(x, minVal), maxVal) for each component in x.  <a href="a00321.html#ga1e28539d3a46965ed9ef92ec7cb3b18a">More...</a><br /></td></tr>
<tr class="separator:ga1e28539d3a46965ed9ef92ec7cb3b18a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga60796d08903489ee185373593bc16b9d"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga60796d08903489ee185373593bc16b9d"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00321.html#ga60796d08903489ee185373593bc16b9d">fclamp</a> (vec&lt; L, T, Q &gt; const &amp;x, T minVal, T maxVal)</td></tr>
<tr class="memdesc:ga60796d08903489ee185373593bc16b9d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns min(max(x, minVal), maxVal) for each component in x.  <a href="a00321.html#ga60796d08903489ee185373593bc16b9d">More...</a><br /></td></tr>
<tr class="separator:ga60796d08903489ee185373593bc16b9d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5c15fa4709763c269c86c0b8b3aa2297"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga5c15fa4709763c269c86c0b8b3aa2297"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00321.html#ga5c15fa4709763c269c86c0b8b3aa2297">fclamp</a> (vec&lt; L, T, Q &gt; const &amp;x, vec&lt; L, T, Q &gt; const &amp;minVal, vec&lt; L, T, Q &gt; const &amp;maxVal)</td></tr>
<tr class="memdesc:ga5c15fa4709763c269c86c0b8b3aa2297"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns min(max(x, minVal), maxVal) for each component in x.  <a href="a00321.html#ga5c15fa4709763c269c86c0b8b3aa2297">More...</a><br /></td></tr>
<tr class="separator:ga5c15fa4709763c269c86c0b8b3aa2297"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae5792cb2b51190057e4aea027eb56f81"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gae5792cb2b51190057e4aea027eb56f81"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00321.html#gae5792cb2b51190057e4aea027eb56f81">fmax</a> (genType x, genType y)</td></tr>
<tr class="memdesc:gae5792cb2b51190057e4aea027eb56f81"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns y if x &lt; y; otherwise, it returns x.  <a href="a00321.html#gae5792cb2b51190057e4aea027eb56f81">More...</a><br /></td></tr>
<tr class="separator:gae5792cb2b51190057e4aea027eb56f81"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa3200559611ac5b9b9ae7283547916a7"><td class="memTemplParams" colspan="2">template&lt;typename genType &gt; </td></tr>
<tr class="memitem:gaa3200559611ac5b9b9ae7283547916a7"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00321.html#gaa3200559611ac5b9b9ae7283547916a7">fmin</a> (genType x, genType y)</td></tr>
<tr class="memdesc:gaa3200559611ac5b9b9ae7283547916a7"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns y if y &lt; x; otherwise, it returns x.  <a href="a00321.html#gaa3200559611ac5b9b9ae7283547916a7">More...</a><br /></td></tr>
<tr class="separator:gaa3200559611ac5b9b9ae7283547916a7"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga04991ccb9865c4c4e58488cfb209ce69"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga04991ccb9865c4c4e58488cfb209ce69"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00321.html#ga04991ccb9865c4c4e58488cfb209ce69">max</a> (T const &amp;x, T const &amp;y, T const &amp;z)</td></tr>
<tr class="memdesc:ga04991ccb9865c4c4e58488cfb209ce69"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the maximum component-wise values of 3 inputs.  <a href="a00321.html#ga04991ccb9865c4c4e58488cfb209ce69">More...</a><br /></td></tr>
<tr class="separator:ga04991ccb9865c4c4e58488cfb209ce69"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae1b7bbe5c91de4924835ea3e14530744"><td class="memTemplParams" colspan="2">template&lt;typename T , template&lt; typename &gt; class C&gt; </td></tr>
<tr class="memitem:gae1b7bbe5c91de4924835ea3e14530744"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL C&lt; T &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00321.html#gae1b7bbe5c91de4924835ea3e14530744">max</a> (C&lt; T &gt; const &amp;x, typename C&lt; T &gt;::T const &amp;y, typename C&lt; T &gt;::T const &amp;z)</td></tr>
<tr class="memdesc:gae1b7bbe5c91de4924835ea3e14530744"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the maximum component-wise values of 3 inputs.  <a href="a00321.html#gae1b7bbe5c91de4924835ea3e14530744">More...</a><br /></td></tr>
<tr class="separator:gae1b7bbe5c91de4924835ea3e14530744"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf832e9d4ab4826b2dda2fda25935a3a4"><td class="memTemplParams" colspan="2">template&lt;typename T , template&lt; typename &gt; class C&gt; </td></tr>
<tr class="memitem:gaf832e9d4ab4826b2dda2fda25935a3a4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL C&lt; T &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00321.html#gaf832e9d4ab4826b2dda2fda25935a3a4">max</a> (C&lt; T &gt; const &amp;x, C&lt; T &gt; const &amp;y, C&lt; T &gt; const &amp;z)</td></tr>
<tr class="memdesc:gaf832e9d4ab4826b2dda2fda25935a3a4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the maximum component-wise values of 3 inputs.  <a href="a00321.html#gaf832e9d4ab4826b2dda2fda25935a3a4">More...</a><br /></td></tr>
<tr class="separator:gaf832e9d4ab4826b2dda2fda25935a3a4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga78e04a0cef1c4863fcae1a2130500d87"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga78e04a0cef1c4863fcae1a2130500d87"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00321.html#ga78e04a0cef1c4863fcae1a2130500d87">max</a> (T const &amp;x, T const &amp;y, T const &amp;z, T const &amp;w)</td></tr>
<tr class="memdesc:ga78e04a0cef1c4863fcae1a2130500d87"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the maximum component-wise values of 4 inputs.  <a href="a00321.html#ga78e04a0cef1c4863fcae1a2130500d87">More...</a><br /></td></tr>
<tr class="separator:ga78e04a0cef1c4863fcae1a2130500d87"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7cca8b53cfda402040494cdf40fbdf4a"><td class="memTemplParams" colspan="2">template&lt;typename T , template&lt; typename &gt; class C&gt; </td></tr>
<tr class="memitem:ga7cca8b53cfda402040494cdf40fbdf4a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL C&lt; T &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00321.html#ga7cca8b53cfda402040494cdf40fbdf4a">max</a> (C&lt; T &gt; const &amp;x, typename C&lt; T &gt;::T const &amp;y, typename C&lt; T &gt;::T const &amp;z, typename C&lt; T &gt;::T const &amp;w)</td></tr>
<tr class="memdesc:ga7cca8b53cfda402040494cdf40fbdf4a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the maximum component-wise values of 4 inputs.  <a href="a00321.html#ga7cca8b53cfda402040494cdf40fbdf4a">More...</a><br /></td></tr>
<tr class="separator:ga7cca8b53cfda402040494cdf40fbdf4a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaacffbc466c2d08c140b181e7fd8a4858"><td class="memTemplParams" colspan="2">template&lt;typename T , template&lt; typename &gt; class C&gt; </td></tr>
<tr class="memitem:gaacffbc466c2d08c140b181e7fd8a4858"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL C&lt; T &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00321.html#gaacffbc466c2d08c140b181e7fd8a4858">max</a> (C&lt; T &gt; const &amp;x, C&lt; T &gt; const &amp;y, C&lt; T &gt; const &amp;z, C&lt; T &gt; const &amp;w)</td></tr>
<tr class="memdesc:gaacffbc466c2d08c140b181e7fd8a4858"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the maximum component-wise values of 4 inputs.  <a href="a00321.html#gaacffbc466c2d08c140b181e7fd8a4858">More...</a><br /></td></tr>
<tr class="separator:gaacffbc466c2d08c140b181e7fd8a4858"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga713d3f9b3e76312c0d314e0c8611a6a6"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga713d3f9b3e76312c0d314e0c8611a6a6"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00321.html#ga713d3f9b3e76312c0d314e0c8611a6a6">min</a> (T const &amp;x, T const &amp;y, T const &amp;z)</td></tr>
<tr class="memdesc:ga713d3f9b3e76312c0d314e0c8611a6a6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the minimum component-wise values of 3 inputs.  <a href="a00321.html#ga713d3f9b3e76312c0d314e0c8611a6a6">More...</a><br /></td></tr>
<tr class="separator:ga713d3f9b3e76312c0d314e0c8611a6a6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga74d1a96e7cdbac40f6d35142d3bcbbd4"><td class="memTemplParams" colspan="2">template&lt;typename T , template&lt; typename &gt; class C&gt; </td></tr>
<tr class="memitem:ga74d1a96e7cdbac40f6d35142d3bcbbd4"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL C&lt; T &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00321.html#ga74d1a96e7cdbac40f6d35142d3bcbbd4">min</a> (C&lt; T &gt; const &amp;x, typename C&lt; T &gt;::T const &amp;y, typename C&lt; T &gt;::T const &amp;z)</td></tr>
<tr class="memdesc:ga74d1a96e7cdbac40f6d35142d3bcbbd4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the minimum component-wise values of 3 inputs.  <a href="a00321.html#ga74d1a96e7cdbac40f6d35142d3bcbbd4">More...</a><br /></td></tr>
<tr class="separator:ga74d1a96e7cdbac40f6d35142d3bcbbd4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga42b5c3fc027fd3d9a50d2ccc9126d9f0"><td class="memTemplParams" colspan="2">template&lt;typename T , template&lt; typename &gt; class C&gt; </td></tr>
<tr class="memitem:ga42b5c3fc027fd3d9a50d2ccc9126d9f0"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL C&lt; T &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00321.html#ga42b5c3fc027fd3d9a50d2ccc9126d9f0">min</a> (C&lt; T &gt; const &amp;x, C&lt; T &gt; const &amp;y, C&lt; T &gt; const &amp;z)</td></tr>
<tr class="memdesc:ga42b5c3fc027fd3d9a50d2ccc9126d9f0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the minimum component-wise values of 3 inputs.  <a href="a00321.html#ga42b5c3fc027fd3d9a50d2ccc9126d9f0">More...</a><br /></td></tr>
<tr class="separator:ga42b5c3fc027fd3d9a50d2ccc9126d9f0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga95466987024d03039607f09e69813d69"><td class="memTemplParams" colspan="2">template&lt;typename T &gt; </td></tr>
<tr class="memitem:ga95466987024d03039607f09e69813d69"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL T&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00321.html#ga95466987024d03039607f09e69813d69">min</a> (T const &amp;x, T const &amp;y, T const &amp;z, T const &amp;w)</td></tr>
<tr class="memdesc:ga95466987024d03039607f09e69813d69"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the minimum component-wise values of 4 inputs.  <a href="a00321.html#ga95466987024d03039607f09e69813d69">More...</a><br /></td></tr>
<tr class="separator:ga95466987024d03039607f09e69813d69"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4fe35dd31dd0c45693c9b60b830b8d47"><td class="memTemplParams" colspan="2">template&lt;typename T , template&lt; typename &gt; class C&gt; </td></tr>
<tr class="memitem:ga4fe35dd31dd0c45693c9b60b830b8d47"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL C&lt; T &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00321.html#ga4fe35dd31dd0c45693c9b60b830b8d47">min</a> (C&lt; T &gt; const &amp;x, typename C&lt; T &gt;::T const &amp;y, typename C&lt; T &gt;::T const &amp;z, typename C&lt; T &gt;::T const &amp;w)</td></tr>
<tr class="memdesc:ga4fe35dd31dd0c45693c9b60b830b8d47"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the minimum component-wise values of 4 inputs.  <a href="a00321.html#ga4fe35dd31dd0c45693c9b60b830b8d47">More...</a><br /></td></tr>
<tr class="separator:ga4fe35dd31dd0c45693c9b60b830b8d47"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7471ea4159eed8dd9ea4ac5d46c2fead"><td class="memTemplParams" colspan="2">template&lt;typename T , template&lt; typename &gt; class C&gt; </td></tr>
<tr class="memitem:ga7471ea4159eed8dd9ea4ac5d46c2fead"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL C&lt; T &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00321.html#ga7471ea4159eed8dd9ea4ac5d46c2fead">min</a> (C&lt; T &gt; const &amp;x, C&lt; T &gt; const &amp;y, C&lt; T &gt; const &amp;z, C&lt; T &gt; const &amp;w)</td></tr>
<tr class="memdesc:ga7471ea4159eed8dd9ea4ac5d46c2fead"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the minimum component-wise values of 4 inputs.  <a href="a00321.html#ga7471ea4159eed8dd9ea4ac5d46c2fead">More...</a><br /></td></tr>
<tr class="separator:ga7471ea4159eed8dd9ea4ac5d46c2fead"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;glm/gtx/extented_min_max.hpp&gt; to use the features of this extension. </p>
<p>Min and max functions for 3 to 4 parameters. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga1e28539d3a46965ed9ef92ec7cb3b18a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::fclamp </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>minVal</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>maxVal</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns min(max(x, minVal), maxVal) for each component in x. </p>
<p>If one of the two arguments is NaN, the value of the other argument is returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>gtx_extented_min_max </dd></dl>

</div>
</div>
<a class="anchor" id="ga60796d08903489ee185373593bc16b9d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::fclamp </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>minVal</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T&#160;</td>
          <td class="paramname"><em>maxVal</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns min(max(x, minVal), maxVal) for each component in x. </p>
<p>If one of the two arguments is NaN, the value of the other argument is returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>gtx_extented_min_max </dd></dl>

</div>
</div>
<a class="anchor" id="ga5c15fa4709763c269c86c0b8b3aa2297"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::fclamp </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>minVal</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>maxVal</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns min(max(x, minVal), maxVal) for each component in x. </p>
<p>If one of the two arguments is NaN, the value of the other argument is returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Floating-point scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>gtx_extented_min_max </dd></dl>

</div>
</div>
<a class="anchor" id="gae5792cb2b51190057e4aea027eb56f81"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::fmax </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns y if x &lt; y; otherwise, it returns x. </p>
<p>If one of the two arguments is NaN, the value of the other argument is returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point; scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>gtx_extented_min_max </dd>
<dd>
<a href="http://en.cppreference.com/w/cpp/numeric/math/fmax">std::fmax documentation</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaa3200559611ac5b9b9ae7283547916a7"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genType glm::fmin </td>
          <td>(</td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genType&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns y if y &lt; x; otherwise, it returns x. </p>
<p>If one of the two arguments is NaN, the value of the other argument is returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genType</td><td>Floating-point or integer; scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd>gtx_extented_min_max </dd></dl>

</div>
</div>
<a class="anchor" id="ga04991ccb9865c4c4e58488cfb209ce69"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::max </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the maximum component-wise values of 3 inputs. </p>
<dl class="section see"><dt>See also</dt><dd>gtx_extented_min_max </dd></dl>

</div>
</div>
<a class="anchor" id="gae1b7bbe5c91de4924835ea3e14530744"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL C&lt;T&gt; glm::max </td>
          <td>(</td>
          <td class="paramtype">C&lt; T &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">typename C&lt; T &gt;::T const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">typename C&lt; T &gt;::T const &amp;&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the maximum component-wise values of 3 inputs. </p>
<dl class="section see"><dt>See also</dt><dd>gtx_extented_min_max </dd></dl>

</div>
</div>
<a class="anchor" id="gaf832e9d4ab4826b2dda2fda25935a3a4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL C&lt;T&gt; glm::max </td>
          <td>(</td>
          <td class="paramtype">C&lt; T &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">C&lt; T &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">C&lt; T &gt; const &amp;&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the maximum component-wise values of 3 inputs. </p>
<dl class="section see"><dt>See also</dt><dd>gtx_extented_min_max </dd></dl>

</div>
</div>
<a class="anchor" id="ga78e04a0cef1c4863fcae1a2130500d87"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::max </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>w</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the maximum component-wise values of 4 inputs. </p>
<dl class="section see"><dt>See also</dt><dd>gtx_extented_min_max </dd></dl>

</div>
</div>
<a class="anchor" id="ga7cca8b53cfda402040494cdf40fbdf4a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL C&lt;T&gt; glm::max </td>
          <td>(</td>
          <td class="paramtype">C&lt; T &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">typename C&lt; T &gt;::T const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">typename C&lt; T &gt;::T const &amp;&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">typename C&lt; T &gt;::T const &amp;&#160;</td>
          <td class="paramname"><em>w</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the maximum component-wise values of 4 inputs. </p>
<dl class="section see"><dt>See also</dt><dd>gtx_extented_min_max </dd></dl>

</div>
</div>
<a class="anchor" id="gaacffbc466c2d08c140b181e7fd8a4858"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL C&lt;T&gt; glm::max </td>
          <td>(</td>
          <td class="paramtype">C&lt; T &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">C&lt; T &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">C&lt; T &gt; const &amp;&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">C&lt; T &gt; const &amp;&#160;</td>
          <td class="paramname"><em>w</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the maximum component-wise values of 4 inputs. </p>
<dl class="section see"><dt>See also</dt><dd>gtx_extented_min_max </dd></dl>

</div>
</div>
<a class="anchor" id="ga713d3f9b3e76312c0d314e0c8611a6a6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::min </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the minimum component-wise values of 3 inputs. </p>
<dl class="section see"><dt>See also</dt><dd>gtx_extented_min_max </dd></dl>

</div>
</div>
<a class="anchor" id="ga74d1a96e7cdbac40f6d35142d3bcbbd4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL C&lt;T&gt; glm::min </td>
          <td>(</td>
          <td class="paramtype">C&lt; T &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">typename C&lt; T &gt;::T const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">typename C&lt; T &gt;::T const &amp;&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the minimum component-wise values of 3 inputs. </p>
<dl class="section see"><dt>See also</dt><dd>gtx_extented_min_max </dd></dl>

</div>
</div>
<a class="anchor" id="ga42b5c3fc027fd3d9a50d2ccc9126d9f0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL C&lt;T&gt; glm::min </td>
          <td>(</td>
          <td class="paramtype">C&lt; T &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">C&lt; T &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">C&lt; T &gt; const &amp;&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the minimum component-wise values of 3 inputs. </p>
<dl class="section see"><dt>See also</dt><dd>gtx_extented_min_max </dd></dl>

</div>
</div>
<a class="anchor" id="ga95466987024d03039607f09e69813d69"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL T glm::min </td>
          <td>(</td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">T const &amp;&#160;</td>
          <td class="paramname"><em>w</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the minimum component-wise values of 4 inputs. </p>
<dl class="section see"><dt>See also</dt><dd>gtx_extented_min_max </dd></dl>

</div>
</div>
<a class="anchor" id="ga4fe35dd31dd0c45693c9b60b830b8d47"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL C&lt;T&gt; glm::min </td>
          <td>(</td>
          <td class="paramtype">C&lt; T &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">typename C&lt; T &gt;::T const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">typename C&lt; T &gt;::T const &amp;&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">typename C&lt; T &gt;::T const &amp;&#160;</td>
          <td class="paramname"><em>w</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the minimum component-wise values of 4 inputs. </p>
<dl class="section see"><dt>See also</dt><dd>gtx_extented_min_max </dd></dl>

</div>
</div>
<a class="anchor" id="ga7471ea4159eed8dd9ea4ac5d46c2fead"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL C&lt;T&gt; glm::min </td>
          <td>(</td>
          <td class="paramtype">C&lt; T &gt; const &amp;&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">C&lt; T &gt; const &amp;&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">C&lt; T &gt; const &amp;&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">C&lt; T &gt; const &amp;&#160;</td>
          <td class="paramname"><em>w</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the minimum component-wise values of 4 inputs. </p>
<dl class="section see"><dt>See also</dt><dd>gtx_extented_min_max </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
