<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTC_bitfield</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTC_bitfield<div class="ingroups"><a class="el" href="a00286.html">Recommended extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00009.html" title="GLM_GTC_bitfield ">glm/gtc/bitfield.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga091d934233a2e121df91b8c7230357c8"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL <a class="el" href="a00304.html#ga518b8d948a6b4ddb72f84d5c3b7b6611">glm::u8vec2</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#ga091d934233a2e121df91b8c7230357c8">bitfieldDeinterleave</a> (<a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">glm::uint16</a> x)</td></tr>
<tr class="memdesc:ga091d934233a2e121df91b8c7230357c8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Deinterleaves the bits of x.  <a href="a00288.html#ga091d934233a2e121df91b8c7230357c8">More...</a><br /></td></tr>
<tr class="separator:ga091d934233a2e121df91b8c7230357c8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7d1cc24dfbcdd932c3a2abbb76235f98"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL <a class="el" href="a00304.html#ga2a78447eb9d66a114b193f4a25899c16">glm::u16vec2</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#ga7d1cc24dfbcdd932c3a2abbb76235f98">bitfieldDeinterleave</a> (<a class="el" href="a00263.html#ga1134b580f8da4de94ca6b1de4d37975e">glm::uint32</a> x)</td></tr>
<tr class="memdesc:ga7d1cc24dfbcdd932c3a2abbb76235f98"><td class="mdescLeft">&#160;</td><td class="mdescRight">Deinterleaves the bits of x.  <a href="a00288.html#ga7d1cc24dfbcdd932c3a2abbb76235f98">More...</a><br /></td></tr>
<tr class="separator:ga7d1cc24dfbcdd932c3a2abbb76235f98"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8dbb8c87092f33bd815dd8a840be5d60"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL <a class="el" href="a00304.html#ga2a266e46ee218d0c680f12b35c500cc0">glm::u32vec2</a>&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#ga8dbb8c87092f33bd815dd8a840be5d60">bitfieldDeinterleave</a> (<a class="el" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">glm::uint64</a> x)</td></tr>
<tr class="memdesc:ga8dbb8c87092f33bd815dd8a840be5d60"><td class="mdescLeft">&#160;</td><td class="mdescRight">Deinterleaves the bits of x.  <a href="a00288.html#ga8dbb8c87092f33bd815dd8a840be5d60">More...</a><br /></td></tr>
<tr class="separator:ga8dbb8c87092f33bd815dd8a840be5d60"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga46f9295abe3b5c7658f5b13c7f819f0a"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:ga46f9295abe3b5c7658f5b13c7f819f0a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00288.html#ga46f9295abe3b5c7658f5b13c7f819f0a">bitfieldFillOne</a> (genIUType Value, int FirstBit, int BitCount)</td></tr>
<tr class="memdesc:ga46f9295abe3b5c7658f5b13c7f819f0a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set to 1 a range of bits.  <a href="a00288.html#ga46f9295abe3b5c7658f5b13c7f819f0a">More...</a><br /></td></tr>
<tr class="separator:ga46f9295abe3b5c7658f5b13c7f819f0a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3e96dd1f0a4bc892f063251ed118c0c1"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga3e96dd1f0a4bc892f063251ed118c0c1"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00288.html#ga3e96dd1f0a4bc892f063251ed118c0c1">bitfieldFillOne</a> (vec&lt; L, T, Q &gt; const &amp;Value, int FirstBit, int BitCount)</td></tr>
<tr class="memdesc:ga3e96dd1f0a4bc892f063251ed118c0c1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set to 1 a range of bits.  <a href="a00288.html#ga3e96dd1f0a4bc892f063251ed118c0c1">More...</a><br /></td></tr>
<tr class="separator:ga3e96dd1f0a4bc892f063251ed118c0c1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga697b86998b7d74ee0a69d8e9f8819fee"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:ga697b86998b7d74ee0a69d8e9f8819fee"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00288.html#ga697b86998b7d74ee0a69d8e9f8819fee">bitfieldFillZero</a> (genIUType Value, int FirstBit, int BitCount)</td></tr>
<tr class="memdesc:ga697b86998b7d74ee0a69d8e9f8819fee"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set to 0 a range of bits.  <a href="a00288.html#ga697b86998b7d74ee0a69d8e9f8819fee">More...</a><br /></td></tr>
<tr class="separator:ga697b86998b7d74ee0a69d8e9f8819fee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga0d16c9acef4be79ea9b47c082a0cf7c2"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga0d16c9acef4be79ea9b47c082a0cf7c2"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00288.html#ga0d16c9acef4be79ea9b47c082a0cf7c2">bitfieldFillZero</a> (vec&lt; L, T, Q &gt; const &amp;Value, int FirstBit, int BitCount)</td></tr>
<tr class="memdesc:ga0d16c9acef4be79ea9b47c082a0cf7c2"><td class="mdescLeft">&#160;</td><td class="mdescRight">Set to 0 a range of bits.  <a href="a00288.html#ga0d16c9acef4be79ea9b47c082a0cf7c2">More...</a><br /></td></tr>
<tr class="separator:ga0d16c9acef4be79ea9b47c082a0cf7c2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga24cad0069f9a0450abd80b3e89501adf"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#ga24cad0069f9a0450abd80b3e89501adf">bitfieldInterleave</a> (int8 x, int8 y)</td></tr>
<tr class="memdesc:ga24cad0069f9a0450abd80b3e89501adf"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interleaves the bits of x and y.  <a href="a00288.html#ga24cad0069f9a0450abd80b3e89501adf">More...</a><br /></td></tr>
<tr class="separator:ga24cad0069f9a0450abd80b3e89501adf"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9a4976a529aec2cee56525e1165da484"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#ga9a4976a529aec2cee56525e1165da484">bitfieldInterleave</a> (uint8 x, uint8 y)</td></tr>
<tr class="memdesc:ga9a4976a529aec2cee56525e1165da484"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interleaves the bits of x and y.  <a href="a00288.html#ga9a4976a529aec2cee56525e1165da484">More...</a><br /></td></tr>
<tr class="separator:ga9a4976a529aec2cee56525e1165da484"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4a76bbca39c40153f3203d0a1926e142"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint16&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#ga4a76bbca39c40153f3203d0a1926e142">bitfieldInterleave</a> (u8vec2 const &amp;v)</td></tr>
<tr class="memdesc:ga4a76bbca39c40153f3203d0a1926e142"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interleaves the bits of x and y.  <a href="a00288.html#ga4a76bbca39c40153f3203d0a1926e142">More...</a><br /></td></tr>
<tr class="separator:ga4a76bbca39c40153f3203d0a1926e142"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac51c33a394593f0631fa3aa5bb778809"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#gac51c33a394593f0631fa3aa5bb778809">bitfieldInterleave</a> (int16 x, int16 y)</td></tr>
<tr class="memdesc:gac51c33a394593f0631fa3aa5bb778809"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interleaves the bits of x and y.  <a href="a00288.html#gac51c33a394593f0631fa3aa5bb778809">More...</a><br /></td></tr>
<tr class="separator:gac51c33a394593f0631fa3aa5bb778809"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga94f3646a5667f4be56f8dcf3310e963f"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#ga94f3646a5667f4be56f8dcf3310e963f">bitfieldInterleave</a> (uint16 x, uint16 y)</td></tr>
<tr class="memdesc:ga94f3646a5667f4be56f8dcf3310e963f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interleaves the bits of x and y.  <a href="a00288.html#ga94f3646a5667f4be56f8dcf3310e963f">More...</a><br /></td></tr>
<tr class="separator:ga94f3646a5667f4be56f8dcf3310e963f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga406c4ee56af4ca37a73f449f154eca3e"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#ga406c4ee56af4ca37a73f449f154eca3e">bitfieldInterleave</a> (u16vec2 const &amp;v)</td></tr>
<tr class="memdesc:ga406c4ee56af4ca37a73f449f154eca3e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interleaves the bits of x and y.  <a href="a00288.html#ga406c4ee56af4ca37a73f449f154eca3e">More...</a><br /></td></tr>
<tr class="separator:ga406c4ee56af4ca37a73f449f154eca3e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaebb756a24a0784e3d6fba8bd011ab77a"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#gaebb756a24a0784e3d6fba8bd011ab77a">bitfieldInterleave</a> (int32 x, int32 y)</td></tr>
<tr class="memdesc:gaebb756a24a0784e3d6fba8bd011ab77a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interleaves the bits of x and y.  <a href="a00288.html#gaebb756a24a0784e3d6fba8bd011ab77a">More...</a><br /></td></tr>
<tr class="separator:gaebb756a24a0784e3d6fba8bd011ab77a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2f1e2b3fe699e7d897ae38b2115ddcbd"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#ga2f1e2b3fe699e7d897ae38b2115ddcbd">bitfieldInterleave</a> (uint32 x, uint32 y)</td></tr>
<tr class="memdesc:ga2f1e2b3fe699e7d897ae38b2115ddcbd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interleaves the bits of x and y.  <a href="a00288.html#ga2f1e2b3fe699e7d897ae38b2115ddcbd">More...</a><br /></td></tr>
<tr class="separator:ga2f1e2b3fe699e7d897ae38b2115ddcbd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8cb17574d60abd6ade84bc57c10e8f78"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#ga8cb17574d60abd6ade84bc57c10e8f78">bitfieldInterleave</a> (u32vec2 const &amp;v)</td></tr>
<tr class="memdesc:ga8cb17574d60abd6ade84bc57c10e8f78"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interleaves the bits of x and y.  <a href="a00288.html#ga8cb17574d60abd6ade84bc57c10e8f78">More...</a><br /></td></tr>
<tr class="separator:ga8cb17574d60abd6ade84bc57c10e8f78"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8fdb724dccd4a07d57efc01147102137"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#ga8fdb724dccd4a07d57efc01147102137">bitfieldInterleave</a> (int8 x, int8 y, int8 z)</td></tr>
<tr class="memdesc:ga8fdb724dccd4a07d57efc01147102137"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interleaves the bits of x, y and z.  <a href="a00288.html#ga8fdb724dccd4a07d57efc01147102137">More...</a><br /></td></tr>
<tr class="separator:ga8fdb724dccd4a07d57efc01147102137"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9fc2a0dd5dcf8b00e113f272a5feca93"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#ga9fc2a0dd5dcf8b00e113f272a5feca93">bitfieldInterleave</a> (uint8 x, uint8 y, uint8 z)</td></tr>
<tr class="memdesc:ga9fc2a0dd5dcf8b00e113f272a5feca93"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interleaves the bits of x, y and z.  <a href="a00288.html#ga9fc2a0dd5dcf8b00e113f272a5feca93">More...</a><br /></td></tr>
<tr class="separator:ga9fc2a0dd5dcf8b00e113f272a5feca93"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa901c36a842fa5d126ea650549f17b24"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#gaa901c36a842fa5d126ea650549f17b24">bitfieldInterleave</a> (int16 x, int16 y, int16 z)</td></tr>
<tr class="memdesc:gaa901c36a842fa5d126ea650549f17b24"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interleaves the bits of x, y and z.  <a href="a00288.html#gaa901c36a842fa5d126ea650549f17b24">More...</a><br /></td></tr>
<tr class="separator:gaa901c36a842fa5d126ea650549f17b24"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3afd6d38881fe3948c53d4214d2197fd"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#ga3afd6d38881fe3948c53d4214d2197fd">bitfieldInterleave</a> (uint16 x, uint16 y, uint16 z)</td></tr>
<tr class="memdesc:ga3afd6d38881fe3948c53d4214d2197fd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interleaves the bits of x, y and z.  <a href="a00288.html#ga3afd6d38881fe3948c53d4214d2197fd">More...</a><br /></td></tr>
<tr class="separator:ga3afd6d38881fe3948c53d4214d2197fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad2075d96a6640121edaa98ea534102ca"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#gad2075d96a6640121edaa98ea534102ca">bitfieldInterleave</a> (int32 x, int32 y, int32 z)</td></tr>
<tr class="memdesc:gad2075d96a6640121edaa98ea534102ca"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interleaves the bits of x, y and z.  <a href="a00288.html#gad2075d96a6640121edaa98ea534102ca">More...</a><br /></td></tr>
<tr class="separator:gad2075d96a6640121edaa98ea534102ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab19fbc739fc0cf7247978602c36f7da8"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#gab19fbc739fc0cf7247978602c36f7da8">bitfieldInterleave</a> (uint32 x, uint32 y, uint32 z)</td></tr>
<tr class="memdesc:gab19fbc739fc0cf7247978602c36f7da8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interleaves the bits of x, y and z.  <a href="a00288.html#gab19fbc739fc0cf7247978602c36f7da8">More...</a><br /></td></tr>
<tr class="separator:gab19fbc739fc0cf7247978602c36f7da8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8a44ae22f5c953b296c42d067dccbe6d"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#ga8a44ae22f5c953b296c42d067dccbe6d">bitfieldInterleave</a> (int8 x, int8 y, int8 z, int8 w)</td></tr>
<tr class="memdesc:ga8a44ae22f5c953b296c42d067dccbe6d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interleaves the bits of x, y, z and w.  <a href="a00288.html#ga8a44ae22f5c953b296c42d067dccbe6d">More...</a><br /></td></tr>
<tr class="separator:ga8a44ae22f5c953b296c42d067dccbe6d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga14bb274d54a3c26f4919dd7ed0dd0c36"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint32&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#ga14bb274d54a3c26f4919dd7ed0dd0c36">bitfieldInterleave</a> (uint8 x, uint8 y, uint8 z, uint8 w)</td></tr>
<tr class="memdesc:ga14bb274d54a3c26f4919dd7ed0dd0c36"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interleaves the bits of x, y, z and w.  <a href="a00288.html#ga14bb274d54a3c26f4919dd7ed0dd0c36">More...</a><br /></td></tr>
<tr class="separator:ga14bb274d54a3c26f4919dd7ed0dd0c36"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga180a63161e1319fbd5a53c84d0429c7a"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL int64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#ga180a63161e1319fbd5a53c84d0429c7a">bitfieldInterleave</a> (int16 x, int16 y, int16 z, int16 w)</td></tr>
<tr class="memdesc:ga180a63161e1319fbd5a53c84d0429c7a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interleaves the bits of x, y, z and w.  <a href="a00288.html#ga180a63161e1319fbd5a53c84d0429c7a">More...</a><br /></td></tr>
<tr class="separator:ga180a63161e1319fbd5a53c84d0429c7a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gafca8768671a14c8016facccb66a89f26"><td class="memItemLeft" align="right" valign="top">GLM_FUNC_DECL uint64&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00288.html#gafca8768671a14c8016facccb66a89f26">bitfieldInterleave</a> (uint16 x, uint16 y, uint16 z, uint16 w)</td></tr>
<tr class="memdesc:gafca8768671a14c8016facccb66a89f26"><td class="mdescLeft">&#160;</td><td class="mdescRight">Interleaves the bits of x, y, z and w.  <a href="a00288.html#gafca8768671a14c8016facccb66a89f26">More...</a><br /></td></tr>
<tr class="separator:gafca8768671a14c8016facccb66a89f26"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2eb49678a344ce1495bdb5586d9896b9"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:ga2eb49678a344ce1495bdb5586d9896b9"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00288.html#ga2eb49678a344ce1495bdb5586d9896b9">bitfieldRotateLeft</a> (genIUType In, int Shift)</td></tr>
<tr class="memdesc:ga2eb49678a344ce1495bdb5586d9896b9"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotate all bits to the left.  <a href="a00288.html#ga2eb49678a344ce1495bdb5586d9896b9">More...</a><br /></td></tr>
<tr class="separator:ga2eb49678a344ce1495bdb5586d9896b9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae186317091b1a39214ebf79008d44a1e"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:gae186317091b1a39214ebf79008d44a1e"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00288.html#gae186317091b1a39214ebf79008d44a1e">bitfieldRotateLeft</a> (vec&lt; L, T, Q &gt; const &amp;In, int Shift)</td></tr>
<tr class="memdesc:gae186317091b1a39214ebf79008d44a1e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotate all bits to the left.  <a href="a00288.html#gae186317091b1a39214ebf79008d44a1e">More...</a><br /></td></tr>
<tr class="separator:gae186317091b1a39214ebf79008d44a1e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1c33d075c5fb8bd8dbfd5092bfc851ca"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:ga1c33d075c5fb8bd8dbfd5092bfc851ca"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00288.html#ga1c33d075c5fb8bd8dbfd5092bfc851ca">bitfieldRotateRight</a> (genIUType In, int Shift)</td></tr>
<tr class="memdesc:ga1c33d075c5fb8bd8dbfd5092bfc851ca"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotate all bits to the right.  <a href="a00288.html#ga1c33d075c5fb8bd8dbfd5092bfc851ca">More...</a><br /></td></tr>
<tr class="separator:ga1c33d075c5fb8bd8dbfd5092bfc851ca"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga590488e1fc00a6cfe5d3bcaf93fbfe88"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga590488e1fc00a6cfe5d3bcaf93fbfe88"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00288.html#ga590488e1fc00a6cfe5d3bcaf93fbfe88">bitfieldRotateRight</a> (vec&lt; L, T, Q &gt; const &amp;In, int Shift)</td></tr>
<tr class="memdesc:ga590488e1fc00a6cfe5d3bcaf93fbfe88"><td class="mdescLeft">&#160;</td><td class="mdescRight">Rotate all bits to the right.  <a href="a00288.html#ga590488e1fc00a6cfe5d3bcaf93fbfe88">More...</a><br /></td></tr>
<tr class="separator:ga590488e1fc00a6cfe5d3bcaf93fbfe88"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad7eba518a0b71662114571ee76939f8a"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:gad7eba518a0b71662114571ee76939f8a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00288.html#gad7eba518a0b71662114571ee76939f8a">mask</a> (genIUType Bits)</td></tr>
<tr class="memdesc:gad7eba518a0b71662114571ee76939f8a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a mask of 'count' bits.  <a href="a00288.html#gad7eba518a0b71662114571ee76939f8a">More...</a><br /></td></tr>
<tr class="separator:gad7eba518a0b71662114571ee76939f8a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2e64e3b922a296033b825311e7f5fff1"><td class="memTemplParams" colspan="2">template&lt;length_t L, typename T , qualifier Q&gt; </td></tr>
<tr class="memitem:ga2e64e3b922a296033b825311e7f5fff1"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL vec&lt; L, T, Q &gt;&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00288.html#ga2e64e3b922a296033b825311e7f5fff1">mask</a> (vec&lt; L, T, Q &gt; const &amp;v)</td></tr>
<tr class="memdesc:ga2e64e3b922a296033b825311e7f5fff1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Build a mask of 'count' bits.  <a href="a00288.html#ga2e64e3b922a296033b825311e7f5fff1">More...</a><br /></td></tr>
<tr class="separator:ga2e64e3b922a296033b825311e7f5fff1"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00009.html" title="GLM_GTC_bitfield ">glm/gtc/bitfield.hpp</a>&gt; to use the features of this extension. </p>
<p>Allow to perform bit operations on integer values </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga091d934233a2e121df91b8c7230357c8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL <a class="el" href="a00304.html#ga518b8d948a6b4ddb72f84d5c3b7b6611">glm::u8vec2</a> glm::bitfieldDeinterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">glm::uint16</a>&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Deinterleaves the bits of x. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga7d1cc24dfbcdd932c3a2abbb76235f98"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL <a class="el" href="a00304.html#ga2a78447eb9d66a114b193f4a25899c16">glm::u16vec2</a> glm::bitfieldDeinterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga1134b580f8da4de94ca6b1de4d37975e">glm::uint32</a>&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Deinterleaves the bits of x. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8dbb8c87092f33bd815dd8a840be5d60"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL <a class="el" href="a00304.html#ga2a266e46ee218d0c680f12b35c500cc0">glm::u32vec2</a> glm::bitfieldDeinterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#gab630f76c26b50298187f7889104d4b9c">glm::uint64</a>&#160;</td>
          <td class="paramname"><em>x</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Deinterleaves the bits of x. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga46f9295abe3b5c7658f5b13c7f819f0a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genIUType glm::bitfieldFillOne </td>
          <td>(</td>
          <td class="paramtype">genIUType&#160;</td>
          <td class="paramname"><em>Value</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>FirstBit</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>BitCount</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set to 1 a range of bits. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga3e96dd1f0a4bc892f063251ed118c0c1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::bitfieldFillOne </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Value</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>FirstBit</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>BitCount</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set to 1 a range of bits. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Signed and unsigned integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga697b86998b7d74ee0a69d8e9f8819fee"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genIUType glm::bitfieldFillZero </td>
          <td>(</td>
          <td class="paramtype">genIUType&#160;</td>
          <td class="paramname"><em>Value</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>FirstBit</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>BitCount</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set to 0 a range of bits. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga0d16c9acef4be79ea9b47c082a0cf7c2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::bitfieldFillZero </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>Value</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>FirstBit</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>BitCount</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Set to 0 a range of bits. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Signed and unsigned integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga24cad0069f9a0450abd80b3e89501adf"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL int16 glm::bitfieldInterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00260.html#ga1b956fe1df85f3c132b21edb4e116458">int8</a>&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00260.html#ga1b956fe1df85f3c132b21edb4e116458">int8</a>&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Interleaves the bits of x and y. </p>
<p>The first bit is the first bit of x followed by the first bit of y. The other bits are interleaved following the previous sequence.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9a4976a529aec2cee56525e1165da484"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint16 glm::bitfieldInterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#gadde6aaee8457bee49c2a92621fe22b79">uint8</a>&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00263.html#gadde6aaee8457bee49c2a92621fe22b79">uint8</a>&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Interleaves the bits of x and y. </p>
<p>The first bit is the first bit of x followed by the first bit of y. The other bits are interleaved following the previous sequence.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga4a76bbca39c40153f3203d0a1926e142"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint16 glm::bitfieldInterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga518b8d948a6b4ddb72f84d5c3b7b6611">u8vec2</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Interleaves the bits of x and y. </p>
<p>The first bit is the first bit of v.x followed by the first bit of v.y. The other bits are interleaved following the previous sequence.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gac51c33a394593f0631fa3aa5bb778809"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL int32 glm::bitfieldInterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00260.html#ga259fa4834387bd68627ddf37bb3ebdb9">int16</a>&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00260.html#ga259fa4834387bd68627ddf37bb3ebdb9">int16</a>&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Interleaves the bits of x and y. </p>
<p>The first bit is the first bit of x followed by the first bit of y. The other bits are interleaved following the previous sequence.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga94f3646a5667f4be56f8dcf3310e963f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint32 glm::bitfieldInterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a>&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a>&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Interleaves the bits of x and y. </p>
<p>The first bit is the first bit of x followed by the first bit of y. The other bits are interleaved following the previous sequence.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga406c4ee56af4ca37a73f449f154eca3e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint32 glm::bitfieldInterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga2a78447eb9d66a114b193f4a25899c16">u16vec2</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Interleaves the bits of x and y. </p>
<p>The first bit is the first bit of v.x followed by the first bit of v.y. The other bits are interleaved following the previous sequence.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaebb756a24a0784e3d6fba8bd011ab77a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL int64 glm::bitfieldInterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00260.html#ga43d43196463bde49cb067f5c20ab8481">int32</a>&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00260.html#ga43d43196463bde49cb067f5c20ab8481">int32</a>&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Interleaves the bits of x and y. </p>
<p>The first bit is the first bit of x followed by the first bit of y. The other bits are interleaved following the previous sequence.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2f1e2b3fe699e7d897ae38b2115ddcbd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint64 glm::bitfieldInterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga1134b580f8da4de94ca6b1de4d37975e">uint32</a>&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00263.html#ga1134b580f8da4de94ca6b1de4d37975e">uint32</a>&#160;</td>
          <td class="paramname"><em>y</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Interleaves the bits of x and y. </p>
<p>The first bit is the first bit of x followed by the first bit of y. The other bits are interleaved following the previous sequence.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8cb17574d60abd6ade84bc57c10e8f78"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint64 glm::bitfieldInterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00304.html#ga2a266e46ee218d0c680f12b35c500cc0">u32vec2</a> const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Interleaves the bits of x and y. </p>
<p>The first bit is the first bit of v.x followed by the first bit of v.y. The other bits are interleaved following the previous sequence.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8fdb724dccd4a07d57efc01147102137"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL int32 glm::bitfieldInterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00260.html#ga1b956fe1df85f3c132b21edb4e116458">int8</a>&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00260.html#ga1b956fe1df85f3c132b21edb4e116458">int8</a>&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00260.html#ga1b956fe1df85f3c132b21edb4e116458">int8</a>&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Interleaves the bits of x, y and z. </p>
<p>The first bit is the first bit of x followed by the first bit of y and the first bit of z. The other bits are interleaved following the previous sequence.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga9fc2a0dd5dcf8b00e113f272a5feca93"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint32 glm::bitfieldInterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#gadde6aaee8457bee49c2a92621fe22b79">uint8</a>&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00263.html#gadde6aaee8457bee49c2a92621fe22b79">uint8</a>&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00263.html#gadde6aaee8457bee49c2a92621fe22b79">uint8</a>&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Interleaves the bits of x, y and z. </p>
<p>The first bit is the first bit of x followed by the first bit of y and the first bit of z. The other bits are interleaved following the previous sequence.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaa901c36a842fa5d126ea650549f17b24"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL int64 glm::bitfieldInterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00260.html#ga259fa4834387bd68627ddf37bb3ebdb9">int16</a>&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00260.html#ga259fa4834387bd68627ddf37bb3ebdb9">int16</a>&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00260.html#ga259fa4834387bd68627ddf37bb3ebdb9">int16</a>&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Interleaves the bits of x, y and z. </p>
<p>The first bit is the first bit of x followed by the first bit of y and the first bit of z. The other bits are interleaved following the previous sequence.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga3afd6d38881fe3948c53d4214d2197fd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint64 glm::bitfieldInterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a>&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a>&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a>&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Interleaves the bits of x, y and z. </p>
<p>The first bit is the first bit of x followed by the first bit of y and the first bit of z. The other bits are interleaved following the previous sequence.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gad2075d96a6640121edaa98ea534102ca"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL int64 glm::bitfieldInterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00260.html#ga43d43196463bde49cb067f5c20ab8481">int32</a>&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00260.html#ga43d43196463bde49cb067f5c20ab8481">int32</a>&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00260.html#ga43d43196463bde49cb067f5c20ab8481">int32</a>&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Interleaves the bits of x, y and z. </p>
<p>The first bit is the first bit of x followed by the first bit of y and the first bit of z. The other bits are interleaved following the previous sequence.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab19fbc739fc0cf7247978602c36f7da8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint64 glm::bitfieldInterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga1134b580f8da4de94ca6b1de4d37975e">uint32</a>&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00263.html#ga1134b580f8da4de94ca6b1de4d37975e">uint32</a>&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00263.html#ga1134b580f8da4de94ca6b1de4d37975e">uint32</a>&#160;</td>
          <td class="paramname"><em>z</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Interleaves the bits of x, y and z. </p>
<p>The first bit is the first bit of x followed by the first bit of y and the first bit of z. The other bits are interleaved following the previous sequence.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga8a44ae22f5c953b296c42d067dccbe6d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL int32 glm::bitfieldInterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00260.html#ga1b956fe1df85f3c132b21edb4e116458">int8</a>&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00260.html#ga1b956fe1df85f3c132b21edb4e116458">int8</a>&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00260.html#ga1b956fe1df85f3c132b21edb4e116458">int8</a>&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00260.html#ga1b956fe1df85f3c132b21edb4e116458">int8</a>&#160;</td>
          <td class="paramname"><em>w</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Interleaves the bits of x, y, z and w. </p>
<p>The first bit is the first bit of x followed by the first bit of y, the first bit of z and finally the first bit of w. The other bits are interleaved following the previous sequence.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga14bb274d54a3c26f4919dd7ed0dd0c36"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint32 glm::bitfieldInterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#gadde6aaee8457bee49c2a92621fe22b79">uint8</a>&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00263.html#gadde6aaee8457bee49c2a92621fe22b79">uint8</a>&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00263.html#gadde6aaee8457bee49c2a92621fe22b79">uint8</a>&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00263.html#gadde6aaee8457bee49c2a92621fe22b79">uint8</a>&#160;</td>
          <td class="paramname"><em>w</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Interleaves the bits of x, y, z and w. </p>
<p>The first bit is the first bit of x followed by the first bit of y, the first bit of z and finally the first bit of w. The other bits are interleaved following the previous sequence.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga180a63161e1319fbd5a53c84d0429c7a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL int64 glm::bitfieldInterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00260.html#ga259fa4834387bd68627ddf37bb3ebdb9">int16</a>&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00260.html#ga259fa4834387bd68627ddf37bb3ebdb9">int16</a>&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00260.html#ga259fa4834387bd68627ddf37bb3ebdb9">int16</a>&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00260.html#ga259fa4834387bd68627ddf37bb3ebdb9">int16</a>&#160;</td>
          <td class="paramname"><em>w</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Interleaves the bits of x, y, z and w. </p>
<p>The first bit is the first bit of x followed by the first bit of y, the first bit of z and finally the first bit of w. The other bits are interleaved following the previous sequence.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gafca8768671a14c8016facccb66a89f26"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL uint64 glm::bitfieldInterleave </td>
          <td>(</td>
          <td class="paramtype"><a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a>&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a>&#160;</td>
          <td class="paramname"><em>y</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a>&#160;</td>
          <td class="paramname"><em>z</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype"><a class="el" href="a00263.html#ga05f6b0ae8f6a6e135b0e290c25fe0e4e">uint16</a>&#160;</td>
          <td class="paramname"><em>w</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Interleaves the bits of x, y, z and w. </p>
<p>The first bit is the first bit of x followed by the first bit of y, the first bit of z and finally the first bit of w. The other bits are interleaved following the previous sequence.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2eb49678a344ce1495bdb5586d9896b9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genIUType glm::bitfieldRotateLeft </td>
          <td>(</td>
          <td class="paramtype">genIUType&#160;</td>
          <td class="paramname"><em>In</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>Shift</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Rotate all bits to the left. </p>
<p>All the bits dropped in the left side are inserted back on the right side.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gae186317091b1a39214ebf79008d44a1e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::bitfieldRotateLeft </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>In</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>Shift</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Rotate all bits to the left. </p>
<p>All the bits dropped in the left side are inserted back on the right side.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Signed and unsigned integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga1c33d075c5fb8bd8dbfd5092bfc851ca"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genIUType glm::bitfieldRotateRight </td>
          <td>(</td>
          <td class="paramtype">genIUType&#160;</td>
          <td class="paramname"><em>In</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>Shift</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Rotate all bits to the right. </p>
<p>All the bits dropped in the right side are inserted back on the left side.</p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga590488e1fc00a6cfe5d3bcaf93fbfe88"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::bitfieldRotateRight </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>In</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>Shift</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Rotate all bits to the right. </p>
<p>All the bits dropped in the right side are inserted back on the left side.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Signed and unsigned integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gad7eba518a0b71662114571ee76939f8a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genIUType glm::mask </td>
          <td>(</td>
          <td class="paramtype">genIUType&#160;</td>
          <td class="paramname"><em>Bits</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a mask of 'count' bits. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga2e64e3b922a296033b825311e7f5fff1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL vec&lt;L, T, Q&gt; glm::mask </td>
          <td>(</td>
          <td class="paramtype">vec&lt; L, T, Q &gt; const &amp;&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Build a mask of 'count' bits. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">L</td><td>Integer between 1 and 4 included that qualify the dimension of the vector </td></tr>
    <tr><td class="paramname">T</td><td>Signed and unsigned integer scalar types </td></tr>
    <tr><td class="paramname">Q</td><td>Value from qualifier enum</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00288.html" title="Include <glm/gtc/bitfield.hpp> to use the features of this extension. ">GLM_GTC_bitfield</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
