<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_GTC_matrix_integer</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#typedef-members">Typedefs</a>  </div>
  <div class="headertitle">
<div class="title">GLM_GTC_matrix_integer<div class="ingroups"><a class="el" href="a00286.html">Recommended extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00100.html" title="GLM_GTC_matrix_integer ">glm/gtc/matrix_integer.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="typedef-members"></a>
Typedefs</h2></td></tr>
<tr class="memitem:ga8499cc3b016003f835314c1c756e9db9"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga8499cc3b016003f835314c1c756e9db9">highp_imat2</a></td></tr>
<tr class="memdesc:ga8499cc3b016003f835314c1c756e9db9"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier signed integer 2x2 matrix.  <a href="a00294.html#ga8499cc3b016003f835314c1c756e9db9">More...</a><br /></td></tr>
<tr class="separator:ga8499cc3b016003f835314c1c756e9db9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa389e2d1c3b10941cae870bc0aeba5b3"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gaa389e2d1c3b10941cae870bc0aeba5b3">highp_imat2x2</a></td></tr>
<tr class="memdesc:gaa389e2d1c3b10941cae870bc0aeba5b3"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier signed integer 2x2 matrix.  <a href="a00294.html#gaa389e2d1c3b10941cae870bc0aeba5b3">More...</a><br /></td></tr>
<tr class="separator:gaa389e2d1c3b10941cae870bc0aeba5b3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaba49d890e06c9444795f5a133fbf1336"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 3, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gaba49d890e06c9444795f5a133fbf1336">highp_imat2x3</a></td></tr>
<tr class="memdesc:gaba49d890e06c9444795f5a133fbf1336"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier signed integer 2x3 matrix.  <a href="a00294.html#gaba49d890e06c9444795f5a133fbf1336">More...</a><br /></td></tr>
<tr class="separator:gaba49d890e06c9444795f5a133fbf1336"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga05a970fd4366dad6c8a0be676b1eae5b"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 4, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga05a970fd4366dad6c8a0be676b1eae5b">highp_imat2x4</a></td></tr>
<tr class="memdesc:ga05a970fd4366dad6c8a0be676b1eae5b"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier signed integer 2x4 matrix.  <a href="a00294.html#ga05a970fd4366dad6c8a0be676b1eae5b">More...</a><br /></td></tr>
<tr class="separator:ga05a970fd4366dad6c8a0be676b1eae5b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaca4506a3efa679eff7c006d9826291fd"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gaca4506a3efa679eff7c006d9826291fd">highp_imat3</a></td></tr>
<tr class="memdesc:gaca4506a3efa679eff7c006d9826291fd"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier signed integer 3x3 matrix.  <a href="a00294.html#gaca4506a3efa679eff7c006d9826291fd">More...</a><br /></td></tr>
<tr class="separator:gaca4506a3efa679eff7c006d9826291fd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga91c671c3ff9706c2393e78b22fd84bcb"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 2, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga91c671c3ff9706c2393e78b22fd84bcb">highp_imat3x2</a></td></tr>
<tr class="memdesc:ga91c671c3ff9706c2393e78b22fd84bcb"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier signed integer 3x2 matrix.  <a href="a00294.html#ga91c671c3ff9706c2393e78b22fd84bcb">More...</a><br /></td></tr>
<tr class="separator:ga91c671c3ff9706c2393e78b22fd84bcb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga07d7b7173e2a6f843ff5f1c615a95b41"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga07d7b7173e2a6f843ff5f1c615a95b41">highp_imat3x3</a></td></tr>
<tr class="memdesc:ga07d7b7173e2a6f843ff5f1c615a95b41"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier signed integer 3x3 matrix.  <a href="a00294.html#ga07d7b7173e2a6f843ff5f1c615a95b41">More...</a><br /></td></tr>
<tr class="separator:ga07d7b7173e2a6f843ff5f1c615a95b41"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga53008f580be99018a17b357b5a4ffc0d"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 4, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga53008f580be99018a17b357b5a4ffc0d">highp_imat3x4</a></td></tr>
<tr class="memdesc:ga53008f580be99018a17b357b5a4ffc0d"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier signed integer 3x4 matrix.  <a href="a00294.html#ga53008f580be99018a17b357b5a4ffc0d">More...</a><br /></td></tr>
<tr class="separator:ga53008f580be99018a17b357b5a4ffc0d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7cfb09b34e0fcf73eaf6512d6483ef56"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga7cfb09b34e0fcf73eaf6512d6483ef56">highp_imat4</a></td></tr>
<tr class="memdesc:ga7cfb09b34e0fcf73eaf6512d6483ef56"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier signed integer 4x4 matrix.  <a href="a00294.html#ga7cfb09b34e0fcf73eaf6512d6483ef56">More...</a><br /></td></tr>
<tr class="separator:ga7cfb09b34e0fcf73eaf6512d6483ef56"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1858820fb292cae396408b2034407f72"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 2, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga1858820fb292cae396408b2034407f72">highp_imat4x2</a></td></tr>
<tr class="memdesc:ga1858820fb292cae396408b2034407f72"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier signed integer 4x2 matrix.  <a href="a00294.html#ga1858820fb292cae396408b2034407f72">More...</a><br /></td></tr>
<tr class="separator:ga1858820fb292cae396408b2034407f72"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6be0b80ae74bb309bc5b964d93d68fc5"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 3, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga6be0b80ae74bb309bc5b964d93d68fc5">highp_imat4x3</a></td></tr>
<tr class="memdesc:ga6be0b80ae74bb309bc5b964d93d68fc5"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier signed integer 4x3 matrix.  <a href="a00294.html#ga6be0b80ae74bb309bc5b964d93d68fc5">More...</a><br /></td></tr>
<tr class="separator:ga6be0b80ae74bb309bc5b964d93d68fc5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2c783ee6f8f040ab37df2f70392c8b44"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, int, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga2c783ee6f8f040ab37df2f70392c8b44">highp_imat4x4</a></td></tr>
<tr class="memdesc:ga2c783ee6f8f040ab37df2f70392c8b44"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier signed integer 4x4 matrix.  <a href="a00294.html#ga2c783ee6f8f040ab37df2f70392c8b44">More...</a><br /></td></tr>
<tr class="separator:ga2c783ee6f8f040ab37df2f70392c8b44"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga42cbce64c4c1cd121b8437daa6e110de"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, uint, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga42cbce64c4c1cd121b8437daa6e110de">highp_umat2</a></td></tr>
<tr class="memdesc:ga42cbce64c4c1cd121b8437daa6e110de"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier unsigned integer 2x2 matrix.  <a href="a00294.html#ga42cbce64c4c1cd121b8437daa6e110de">More...</a><br /></td></tr>
<tr class="separator:ga42cbce64c4c1cd121b8437daa6e110de"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5337b7bc95f9cbac08a0c00b3f936b28"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, uint, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga5337b7bc95f9cbac08a0c00b3f936b28">highp_umat2x2</a></td></tr>
<tr class="memdesc:ga5337b7bc95f9cbac08a0c00b3f936b28"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier unsigned integer 2x2 matrix.  <a href="a00294.html#ga5337b7bc95f9cbac08a0c00b3f936b28">More...</a><br /></td></tr>
<tr class="separator:ga5337b7bc95f9cbac08a0c00b3f936b28"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga90718c7128320b24b52f9ea70e643ad4"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 3, uint, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga90718c7128320b24b52f9ea70e643ad4">highp_umat2x3</a></td></tr>
<tr class="memdesc:ga90718c7128320b24b52f9ea70e643ad4"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier unsigned integer 2x3 matrix.  <a href="a00294.html#ga90718c7128320b24b52f9ea70e643ad4">More...</a><br /></td></tr>
<tr class="separator:ga90718c7128320b24b52f9ea70e643ad4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadca0a4724b4a6f56a2355b6f6e19248b"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 4, uint, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gadca0a4724b4a6f56a2355b6f6e19248b">highp_umat2x4</a></td></tr>
<tr class="memdesc:gadca0a4724b4a6f56a2355b6f6e19248b"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier unsigned integer 2x4 matrix.  <a href="a00294.html#gadca0a4724b4a6f56a2355b6f6e19248b">More...</a><br /></td></tr>
<tr class="separator:gadca0a4724b4a6f56a2355b6f6e19248b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa1143120339b7d2d469d327662e8a172"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, uint, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gaa1143120339b7d2d469d327662e8a172">highp_umat3</a></td></tr>
<tr class="memdesc:gaa1143120339b7d2d469d327662e8a172"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier unsigned integer 3x3 matrix.  <a href="a00294.html#gaa1143120339b7d2d469d327662e8a172">More...</a><br /></td></tr>
<tr class="separator:gaa1143120339b7d2d469d327662e8a172"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga844a5da2e7fc03fc7cccc7f1b70809c4"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 2, uint, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga844a5da2e7fc03fc7cccc7f1b70809c4">highp_umat3x2</a></td></tr>
<tr class="memdesc:ga844a5da2e7fc03fc7cccc7f1b70809c4"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier unsigned integer 3x2 matrix.  <a href="a00294.html#ga844a5da2e7fc03fc7cccc7f1b70809c4">More...</a><br /></td></tr>
<tr class="separator:ga844a5da2e7fc03fc7cccc7f1b70809c4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1f7d41c36b980774a4d2e7c1647fb4b2"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, uint, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga1f7d41c36b980774a4d2e7c1647fb4b2">highp_umat3x3</a></td></tr>
<tr class="memdesc:ga1f7d41c36b980774a4d2e7c1647fb4b2"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier unsigned integer 3x3 matrix.  <a href="a00294.html#ga1f7d41c36b980774a4d2e7c1647fb4b2">More...</a><br /></td></tr>
<tr class="separator:ga1f7d41c36b980774a4d2e7c1647fb4b2"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga25ee15c323924f2d0fe9896d329e5086"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 4, uint, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga25ee15c323924f2d0fe9896d329e5086">highp_umat3x4</a></td></tr>
<tr class="memdesc:ga25ee15c323924f2d0fe9896d329e5086"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier unsigned integer 3x4 matrix.  <a href="a00294.html#ga25ee15c323924f2d0fe9896d329e5086">More...</a><br /></td></tr>
<tr class="separator:ga25ee15c323924f2d0fe9896d329e5086"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf665e4e78c2cc32a54ab40325738f9c9"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, uint, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gaf665e4e78c2cc32a54ab40325738f9c9">highp_umat4</a></td></tr>
<tr class="memdesc:gaf665e4e78c2cc32a54ab40325738f9c9"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier unsigned integer 4x4 matrix.  <a href="a00294.html#gaf665e4e78c2cc32a54ab40325738f9c9">More...</a><br /></td></tr>
<tr class="separator:gaf665e4e78c2cc32a54ab40325738f9c9"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae69eb82ec08b0dc9bf2ead2a339ff801"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 2, uint, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gae69eb82ec08b0dc9bf2ead2a339ff801">highp_umat4x2</a></td></tr>
<tr class="memdesc:gae69eb82ec08b0dc9bf2ead2a339ff801"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier unsigned integer 4x2 matrix.  <a href="a00294.html#gae69eb82ec08b0dc9bf2ead2a339ff801">More...</a><br /></td></tr>
<tr class="separator:gae69eb82ec08b0dc9bf2ead2a339ff801"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga45a8163d02c43216252056b0c120f3a5"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 3, uint, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga45a8163d02c43216252056b0c120f3a5">highp_umat4x3</a></td></tr>
<tr class="memdesc:ga45a8163d02c43216252056b0c120f3a5"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier unsigned integer 4x3 matrix.  <a href="a00294.html#ga45a8163d02c43216252056b0c120f3a5">More...</a><br /></td></tr>
<tr class="separator:ga45a8163d02c43216252056b0c120f3a5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6a56cbb769aed334c95241664415f9ba"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, uint, highp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga6a56cbb769aed334c95241664415f9ba">highp_umat4x4</a></td></tr>
<tr class="memdesc:ga6a56cbb769aed334c95241664415f9ba"><td class="mdescLeft">&#160;</td><td class="mdescRight">High-qualifier unsigned integer 4x4 matrix.  <a href="a00294.html#ga6a56cbb769aed334c95241664415f9ba">More...</a><br /></td></tr>
<tr class="separator:ga6a56cbb769aed334c95241664415f9ba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaabe04f9948d4a213bb1c20137de03e01"><td class="memItemLeft" align="right" valign="top">typedef mediump_imat2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gaabe04f9948d4a213bb1c20137de03e01">imat2</a></td></tr>
<tr class="memdesc:gaabe04f9948d4a213bb1c20137de03e01"><td class="mdescLeft">&#160;</td><td class="mdescRight">Signed integer 2x2 matrix.  <a href="a00294.html#gaabe04f9948d4a213bb1c20137de03e01">More...</a><br /></td></tr>
<tr class="separator:gaabe04f9948d4a213bb1c20137de03e01"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa4732a240522ad9bc28144fda2fc14ec"><td class="memItemLeft" align="right" valign="top">typedef mediump_imat2x2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gaa4732a240522ad9bc28144fda2fc14ec">imat2x2</a></td></tr>
<tr class="memdesc:gaa4732a240522ad9bc28144fda2fc14ec"><td class="mdescLeft">&#160;</td><td class="mdescRight">Signed integer 2x2 matrix.  <a href="a00294.html#gaa4732a240522ad9bc28144fda2fc14ec">More...</a><br /></td></tr>
<tr class="separator:gaa4732a240522ad9bc28144fda2fc14ec"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3f42dd3d5d94a0fd5706f7ec8dd0c605"><td class="memItemLeft" align="right" valign="top">typedef mediump_imat2x3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga3f42dd3d5d94a0fd5706f7ec8dd0c605">imat2x3</a></td></tr>
<tr class="memdesc:ga3f42dd3d5d94a0fd5706f7ec8dd0c605"><td class="mdescLeft">&#160;</td><td class="mdescRight">Signed integer 2x3 matrix.  <a href="a00294.html#ga3f42dd3d5d94a0fd5706f7ec8dd0c605">More...</a><br /></td></tr>
<tr class="separator:ga3f42dd3d5d94a0fd5706f7ec8dd0c605"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9d8faafdca42583d67e792dd038fc668"><td class="memItemLeft" align="right" valign="top">typedef mediump_imat2x4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga9d8faafdca42583d67e792dd038fc668">imat2x4</a></td></tr>
<tr class="memdesc:ga9d8faafdca42583d67e792dd038fc668"><td class="mdescLeft">&#160;</td><td class="mdescRight">Signed integer 2x4 matrix.  <a href="a00294.html#ga9d8faafdca42583d67e792dd038fc668">More...</a><br /></td></tr>
<tr class="separator:ga9d8faafdca42583d67e792dd038fc668"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga038f68437155ffa3c2583a15264a8195"><td class="memItemLeft" align="right" valign="top">typedef mediump_imat3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga038f68437155ffa3c2583a15264a8195">imat3</a></td></tr>
<tr class="memdesc:ga038f68437155ffa3c2583a15264a8195"><td class="mdescLeft">&#160;</td><td class="mdescRight">Signed integer 3x3 matrix.  <a href="a00294.html#ga038f68437155ffa3c2583a15264a8195">More...</a><br /></td></tr>
<tr class="separator:ga038f68437155ffa3c2583a15264a8195"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7b33bbe4f12c060892bd3cc8d4cd737f"><td class="memItemLeft" align="right" valign="top">typedef mediump_imat3x2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga7b33bbe4f12c060892bd3cc8d4cd737f">imat3x2</a></td></tr>
<tr class="memdesc:ga7b33bbe4f12c060892bd3cc8d4cd737f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Signed integer 3x2 matrix.  <a href="a00294.html#ga7b33bbe4f12c060892bd3cc8d4cd737f">More...</a><br /></td></tr>
<tr class="separator:ga7b33bbe4f12c060892bd3cc8d4cd737f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6aacc960f62e8f7d2fe9d32d5050e7a4"><td class="memItemLeft" align="right" valign="top">typedef mediump_imat3x3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga6aacc960f62e8f7d2fe9d32d5050e7a4">imat3x3</a></td></tr>
<tr class="memdesc:ga6aacc960f62e8f7d2fe9d32d5050e7a4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Signed integer 3x3 matrix.  <a href="a00294.html#ga6aacc960f62e8f7d2fe9d32d5050e7a4">More...</a><br /></td></tr>
<tr class="separator:ga6aacc960f62e8f7d2fe9d32d5050e7a4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6e9ce23496d8b08dfc302d4039694b58"><td class="memItemLeft" align="right" valign="top">typedef mediump_imat3x4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga6e9ce23496d8b08dfc302d4039694b58">imat3x4</a></td></tr>
<tr class="memdesc:ga6e9ce23496d8b08dfc302d4039694b58"><td class="mdescLeft">&#160;</td><td class="mdescRight">Signed integer 3x4 matrix.  <a href="a00294.html#ga6e9ce23496d8b08dfc302d4039694b58">More...</a><br /></td></tr>
<tr class="separator:ga6e9ce23496d8b08dfc302d4039694b58"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga96b0d26a33b81bb6a60ca0f39682f7eb"><td class="memItemLeft" align="right" valign="top">typedef mediump_imat4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga96b0d26a33b81bb6a60ca0f39682f7eb">imat4</a></td></tr>
<tr class="memdesc:ga96b0d26a33b81bb6a60ca0f39682f7eb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Signed integer 4x4 matrix.  <a href="a00294.html#ga96b0d26a33b81bb6a60ca0f39682f7eb">More...</a><br /></td></tr>
<tr class="separator:ga96b0d26a33b81bb6a60ca0f39682f7eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8ce7ef51d8b2c1901fa5414deccbc3fa"><td class="memItemLeft" align="right" valign="top">typedef mediump_imat4x2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga8ce7ef51d8b2c1901fa5414deccbc3fa">imat4x2</a></td></tr>
<tr class="memdesc:ga8ce7ef51d8b2c1901fa5414deccbc3fa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Signed integer 4x2 matrix.  <a href="a00294.html#ga8ce7ef51d8b2c1901fa5414deccbc3fa">More...</a><br /></td></tr>
<tr class="separator:ga8ce7ef51d8b2c1901fa5414deccbc3fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga705ee0bf49d6c3de4404ce2481bf0df5"><td class="memItemLeft" align="right" valign="top">typedef mediump_imat4x3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga705ee0bf49d6c3de4404ce2481bf0df5">imat4x3</a></td></tr>
<tr class="memdesc:ga705ee0bf49d6c3de4404ce2481bf0df5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Signed integer 4x3 matrix.  <a href="a00294.html#ga705ee0bf49d6c3de4404ce2481bf0df5">More...</a><br /></td></tr>
<tr class="separator:ga705ee0bf49d6c3de4404ce2481bf0df5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga43ed5e4f475b6f4cad7cba78f29c405b"><td class="memItemLeft" align="right" valign="top">typedef mediump_imat4x4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga43ed5e4f475b6f4cad7cba78f29c405b">imat4x4</a></td></tr>
<tr class="memdesc:ga43ed5e4f475b6f4cad7cba78f29c405b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Signed integer 4x4 matrix.  <a href="a00294.html#ga43ed5e4f475b6f4cad7cba78f29c405b">More...</a><br /></td></tr>
<tr class="separator:ga43ed5e4f475b6f4cad7cba78f29c405b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa0bff0be804142bb16d441aec0a7962e"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, int, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gaa0bff0be804142bb16d441aec0a7962e">lowp_imat2</a></td></tr>
<tr class="memdesc:gaa0bff0be804142bb16d441aec0a7962e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier signed integer 2x2 matrix.  <a href="a00294.html#gaa0bff0be804142bb16d441aec0a7962e">More...</a><br /></td></tr>
<tr class="separator:gaa0bff0be804142bb16d441aec0a7962e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga92b95b679975d408645547ab45a8dcd8"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, int, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga92b95b679975d408645547ab45a8dcd8">lowp_imat2x2</a></td></tr>
<tr class="memdesc:ga92b95b679975d408645547ab45a8dcd8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier signed integer 2x2 matrix.  <a href="a00294.html#ga92b95b679975d408645547ab45a8dcd8">More...</a><br /></td></tr>
<tr class="separator:ga92b95b679975d408645547ab45a8dcd8"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8c9e7a388f8e7c52f1e6857dee8afb65"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 3, int, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga8c9e7a388f8e7c52f1e6857dee8afb65">lowp_imat2x3</a></td></tr>
<tr class="memdesc:ga8c9e7a388f8e7c52f1e6857dee8afb65"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier signed integer 2x3 matrix.  <a href="a00294.html#ga8c9e7a388f8e7c52f1e6857dee8afb65">More...</a><br /></td></tr>
<tr class="separator:ga8c9e7a388f8e7c52f1e6857dee8afb65"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9cc13bd1f8dd2933e9fa31fe3f70e16e"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 4, int, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga9cc13bd1f8dd2933e9fa31fe3f70e16e">lowp_imat2x4</a></td></tr>
<tr class="memdesc:ga9cc13bd1f8dd2933e9fa31fe3f70e16e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier signed integer 2x4 matrix.  <a href="a00294.html#ga9cc13bd1f8dd2933e9fa31fe3f70e16e">More...</a><br /></td></tr>
<tr class="separator:ga9cc13bd1f8dd2933e9fa31fe3f70e16e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga69bfe668f4170379fc1f35d82b060c43"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, int, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga69bfe668f4170379fc1f35d82b060c43">lowp_imat3</a></td></tr>
<tr class="memdesc:ga69bfe668f4170379fc1f35d82b060c43"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier signed integer 3x3 matrix.  <a href="a00294.html#ga69bfe668f4170379fc1f35d82b060c43">More...</a><br /></td></tr>
<tr class="separator:ga69bfe668f4170379fc1f35d82b060c43"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga33db8f27491d30906cd37c0d86b3f432"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 2, int, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga33db8f27491d30906cd37c0d86b3f432">lowp_imat3x2</a></td></tr>
<tr class="memdesc:ga33db8f27491d30906cd37c0d86b3f432"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier signed integer 3x2 matrix.  <a href="a00294.html#ga33db8f27491d30906cd37c0d86b3f432">More...</a><br /></td></tr>
<tr class="separator:ga33db8f27491d30906cd37c0d86b3f432"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga664f061df00020048c3f8530329ace45"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, int, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga664f061df00020048c3f8530329ace45">lowp_imat3x3</a></td></tr>
<tr class="memdesc:ga664f061df00020048c3f8530329ace45"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier signed integer 3x3 matrix.  <a href="a00294.html#ga664f061df00020048c3f8530329ace45">More...</a><br /></td></tr>
<tr class="separator:ga664f061df00020048c3f8530329ace45"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9273faab33623d944af4080befbb2c80"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 4, int, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga9273faab33623d944af4080befbb2c80">lowp_imat3x4</a></td></tr>
<tr class="memdesc:ga9273faab33623d944af4080befbb2c80"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier signed integer 3x4 matrix.  <a href="a00294.html#ga9273faab33623d944af4080befbb2c80">More...</a><br /></td></tr>
<tr class="separator:ga9273faab33623d944af4080befbb2c80"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad1e77f7270cad461ca4fcb4c3ec2e98c"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, int, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gad1e77f7270cad461ca4fcb4c3ec2e98c">lowp_imat4</a></td></tr>
<tr class="memdesc:gad1e77f7270cad461ca4fcb4c3ec2e98c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier signed integer 4x4 matrix.  <a href="a00294.html#gad1e77f7270cad461ca4fcb4c3ec2e98c">More...</a><br /></td></tr>
<tr class="separator:gad1e77f7270cad461ca4fcb4c3ec2e98c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga26ec1a2ba08a1488f5f05336858a0f09"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 2, int, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga26ec1a2ba08a1488f5f05336858a0f09">lowp_imat4x2</a></td></tr>
<tr class="memdesc:ga26ec1a2ba08a1488f5f05336858a0f09"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier signed integer 4x2 matrix.  <a href="a00294.html#ga26ec1a2ba08a1488f5f05336858a0f09">More...</a><br /></td></tr>
<tr class="separator:ga26ec1a2ba08a1488f5f05336858a0f09"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8f40483a3ae634ead8ad22272c543a33"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 3, int, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga8f40483a3ae634ead8ad22272c543a33">lowp_imat4x3</a></td></tr>
<tr class="memdesc:ga8f40483a3ae634ead8ad22272c543a33"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier signed integer 4x3 matrix.  <a href="a00294.html#ga8f40483a3ae634ead8ad22272c543a33">More...</a><br /></td></tr>
<tr class="separator:ga8f40483a3ae634ead8ad22272c543a33"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf65677e53ac8e31a107399340d5e2451"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, int, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gaf65677e53ac8e31a107399340d5e2451">lowp_imat4x4</a></td></tr>
<tr class="memdesc:gaf65677e53ac8e31a107399340d5e2451"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier signed integer 4x4 matrix.  <a href="a00294.html#gaf65677e53ac8e31a107399340d5e2451">More...</a><br /></td></tr>
<tr class="separator:gaf65677e53ac8e31a107399340d5e2451"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf2fba702d990437fc88ff3f3a76846ee"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, uint, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gaf2fba702d990437fc88ff3f3a76846ee">lowp_umat2</a></td></tr>
<tr class="memdesc:gaf2fba702d990437fc88ff3f3a76846ee"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier unsigned integer 2x2 matrix.  <a href="a00294.html#gaf2fba702d990437fc88ff3f3a76846ee">More...</a><br /></td></tr>
<tr class="separator:gaf2fba702d990437fc88ff3f3a76846ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7b2e9d89745f7175051284e54c81d81c"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, uint, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga7b2e9d89745f7175051284e54c81d81c">lowp_umat2x2</a></td></tr>
<tr class="memdesc:ga7b2e9d89745f7175051284e54c81d81c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier unsigned integer 2x2 matrix.  <a href="a00294.html#ga7b2e9d89745f7175051284e54c81d81c">More...</a><br /></td></tr>
<tr class="separator:ga7b2e9d89745f7175051284e54c81d81c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3072f90fd86f17a862e21589fbb14c0f"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 3, uint, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga3072f90fd86f17a862e21589fbb14c0f">lowp_umat2x3</a></td></tr>
<tr class="memdesc:ga3072f90fd86f17a862e21589fbb14c0f"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier unsigned integer 2x3 matrix.  <a href="a00294.html#ga3072f90fd86f17a862e21589fbb14c0f">More...</a><br /></td></tr>
<tr class="separator:ga3072f90fd86f17a862e21589fbb14c0f"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8bb45fec4bd77bd81b4ae7eb961a270d"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 4, uint, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga8bb45fec4bd77bd81b4ae7eb961a270d">lowp_umat2x4</a></td></tr>
<tr class="memdesc:ga8bb45fec4bd77bd81b4ae7eb961a270d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier unsigned integer 2x4 matrix.  <a href="a00294.html#ga8bb45fec4bd77bd81b4ae7eb961a270d">More...</a><br /></td></tr>
<tr class="separator:ga8bb45fec4bd77bd81b4ae7eb961a270d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf1145f72bcdd590f5808c4bc170c2924"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, uint, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gaf1145f72bcdd590f5808c4bc170c2924">lowp_umat3</a></td></tr>
<tr class="memdesc:gaf1145f72bcdd590f5808c4bc170c2924"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier unsigned integer 3x3 matrix.  <a href="a00294.html#gaf1145f72bcdd590f5808c4bc170c2924">More...</a><br /></td></tr>
<tr class="separator:gaf1145f72bcdd590f5808c4bc170c2924"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga56ea68c6a6cba8d8c21d17bb14e69c6b"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 2, uint, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga56ea68c6a6cba8d8c21d17bb14e69c6b">lowp_umat3x2</a></td></tr>
<tr class="memdesc:ga56ea68c6a6cba8d8c21d17bb14e69c6b"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier unsigned integer 3x2 matrix.  <a href="a00294.html#ga56ea68c6a6cba8d8c21d17bb14e69c6b">More...</a><br /></td></tr>
<tr class="separator:ga56ea68c6a6cba8d8c21d17bb14e69c6b"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4f660a39a395cc14f018f985e7dfbeb5"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, uint, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga4f660a39a395cc14f018f985e7dfbeb5">lowp_umat3x3</a></td></tr>
<tr class="memdesc:ga4f660a39a395cc14f018f985e7dfbeb5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier unsigned integer 3x3 matrix.  <a href="a00294.html#ga4f660a39a395cc14f018f985e7dfbeb5">More...</a><br /></td></tr>
<tr class="separator:ga4f660a39a395cc14f018f985e7dfbeb5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaec3d624306bd59649f021864709d56b5"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 4, uint, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gaec3d624306bd59649f021864709d56b5">lowp_umat3x4</a></td></tr>
<tr class="memdesc:gaec3d624306bd59649f021864709d56b5"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier unsigned integer 3x4 matrix.  <a href="a00294.html#gaec3d624306bd59649f021864709d56b5">More...</a><br /></td></tr>
<tr class="separator:gaec3d624306bd59649f021864709d56b5"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac092c6105827bf9ea080db38074b78eb"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, uint, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gac092c6105827bf9ea080db38074b78eb">lowp_umat4</a></td></tr>
<tr class="memdesc:gac092c6105827bf9ea080db38074b78eb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier unsigned integer 4x4 matrix.  <a href="a00294.html#gac092c6105827bf9ea080db38074b78eb">More...</a><br /></td></tr>
<tr class="separator:gac092c6105827bf9ea080db38074b78eb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga7716c2b210d141846f1ac4e774adef5e"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 2, uint, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga7716c2b210d141846f1ac4e774adef5e">lowp_umat4x2</a></td></tr>
<tr class="memdesc:ga7716c2b210d141846f1ac4e774adef5e"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier unsigned integer 4x2 matrix.  <a href="a00294.html#ga7716c2b210d141846f1ac4e774adef5e">More...</a><br /></td></tr>
<tr class="separator:ga7716c2b210d141846f1ac4e774adef5e"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga09ab33a2636f5f43f7fae29cfbc20fff"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 3, uint, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga09ab33a2636f5f43f7fae29cfbc20fff">lowp_umat4x3</a></td></tr>
<tr class="memdesc:ga09ab33a2636f5f43f7fae29cfbc20fff"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier unsigned integer 4x3 matrix.  <a href="a00294.html#ga09ab33a2636f5f43f7fae29cfbc20fff">More...</a><br /></td></tr>
<tr class="separator:ga09ab33a2636f5f43f7fae29cfbc20fff"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga10aafc66cf1a0ece336b1c5ae13d0cc0"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, uint, lowp &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga10aafc66cf1a0ece336b1c5ae13d0cc0">lowp_umat4x4</a></td></tr>
<tr class="memdesc:ga10aafc66cf1a0ece336b1c5ae13d0cc0"><td class="mdescLeft">&#160;</td><td class="mdescRight">Low-qualifier unsigned integer 4x4 matrix.  <a href="a00294.html#ga10aafc66cf1a0ece336b1c5ae13d0cc0">More...</a><br /></td></tr>
<tr class="separator:ga10aafc66cf1a0ece336b1c5ae13d0cc0"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga20f4cc7ab23e2aa1f4db9fdb5496d378"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, int, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga20f4cc7ab23e2aa1f4db9fdb5496d378">mediump_imat2</a></td></tr>
<tr class="memdesc:ga20f4cc7ab23e2aa1f4db9fdb5496d378"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier signed integer 2x2 matrix.  <a href="a00294.html#ga20f4cc7ab23e2aa1f4db9fdb5496d378">More...</a><br /></td></tr>
<tr class="separator:ga20f4cc7ab23e2aa1f4db9fdb5496d378"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4b2aeb11a329940721dda9583e71f856"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, int, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga4b2aeb11a329940721dda9583e71f856">mediump_imat2x2</a></td></tr>
<tr class="memdesc:ga4b2aeb11a329940721dda9583e71f856"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier signed integer 2x2 matrix.  <a href="a00294.html#ga4b2aeb11a329940721dda9583e71f856">More...</a><br /></td></tr>
<tr class="separator:ga4b2aeb11a329940721dda9583e71f856"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga74362470ba99843ac70aee5ac38cc674"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 3, int, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga74362470ba99843ac70aee5ac38cc674">mediump_imat2x3</a></td></tr>
<tr class="memdesc:ga74362470ba99843ac70aee5ac38cc674"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier signed integer 2x3 matrix.  <a href="a00294.html#ga74362470ba99843ac70aee5ac38cc674">More...</a><br /></td></tr>
<tr class="separator:ga74362470ba99843ac70aee5ac38cc674"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8da25cd380ba30fc5b68a4687deb3e09"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 4, int, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga8da25cd380ba30fc5b68a4687deb3e09">mediump_imat2x4</a></td></tr>
<tr class="memdesc:ga8da25cd380ba30fc5b68a4687deb3e09"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier signed integer 2x4 matrix.  <a href="a00294.html#ga8da25cd380ba30fc5b68a4687deb3e09">More...</a><br /></td></tr>
<tr class="separator:ga8da25cd380ba30fc5b68a4687deb3e09"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga6c63bdc736efd3466e0730de0251cb71"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, int, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga6c63bdc736efd3466e0730de0251cb71">mediump_imat3</a></td></tr>
<tr class="memdesc:ga6c63bdc736efd3466e0730de0251cb71"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier signed integer 3x3 matrix.  <a href="a00294.html#ga6c63bdc736efd3466e0730de0251cb71">More...</a><br /></td></tr>
<tr class="separator:ga6c63bdc736efd3466e0730de0251cb71"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gac0b4e42d648fb3eaf4bb88da82ecc809"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 2, int, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gac0b4e42d648fb3eaf4bb88da82ecc809">mediump_imat3x2</a></td></tr>
<tr class="memdesc:gac0b4e42d648fb3eaf4bb88da82ecc809"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier signed integer 3x2 matrix.  <a href="a00294.html#gac0b4e42d648fb3eaf4bb88da82ecc809">More...</a><br /></td></tr>
<tr class="separator:gac0b4e42d648fb3eaf4bb88da82ecc809"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gad99cc2aad8fc57f068cfa7719dbbea12"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, int, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gad99cc2aad8fc57f068cfa7719dbbea12">mediump_imat3x3</a></td></tr>
<tr class="memdesc:gad99cc2aad8fc57f068cfa7719dbbea12"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier signed integer 3x3 matrix.  <a href="a00294.html#gad99cc2aad8fc57f068cfa7719dbbea12">More...</a><br /></td></tr>
<tr class="separator:gad99cc2aad8fc57f068cfa7719dbbea12"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga67689a518b181a26540bc44a163525cd"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 4, int, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga67689a518b181a26540bc44a163525cd">mediump_imat3x4</a></td></tr>
<tr class="memdesc:ga67689a518b181a26540bc44a163525cd"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier signed integer 3x4 matrix.  <a href="a00294.html#ga67689a518b181a26540bc44a163525cd">More...</a><br /></td></tr>
<tr class="separator:ga67689a518b181a26540bc44a163525cd"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf348552978553630d2a00b78eb887ced"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, int, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gaf348552978553630d2a00b78eb887ced">mediump_imat4</a></td></tr>
<tr class="memdesc:gaf348552978553630d2a00b78eb887ced"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier signed integer 4x4 matrix.  <a href="a00294.html#gaf348552978553630d2a00b78eb887ced">More...</a><br /></td></tr>
<tr class="separator:gaf348552978553630d2a00b78eb887ced"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga8b2d35816f7103f0f4c82dd2f27571fc"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 2, int, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga8b2d35816f7103f0f4c82dd2f27571fc">mediump_imat4x2</a></td></tr>
<tr class="memdesc:ga8b2d35816f7103f0f4c82dd2f27571fc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier signed integer 4x2 matrix.  <a href="a00294.html#ga8b2d35816f7103f0f4c82dd2f27571fc">More...</a><br /></td></tr>
<tr class="separator:ga8b2d35816f7103f0f4c82dd2f27571fc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5b10acc696759e03f6ab918f4467e94c"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 3, int, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga5b10acc696759e03f6ab918f4467e94c">mediump_imat4x3</a></td></tr>
<tr class="memdesc:ga5b10acc696759e03f6ab918f4467e94c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier signed integer 4x3 matrix.  <a href="a00294.html#ga5b10acc696759e03f6ab918f4467e94c">More...</a><br /></td></tr>
<tr class="separator:ga5b10acc696759e03f6ab918f4467e94c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga2596869d154dec1180beadbb9df80501"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, int, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga2596869d154dec1180beadbb9df80501">mediump_imat4x4</a></td></tr>
<tr class="memdesc:ga2596869d154dec1180beadbb9df80501"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier signed integer 4x4 matrix.  <a href="a00294.html#ga2596869d154dec1180beadbb9df80501">More...</a><br /></td></tr>
<tr class="separator:ga2596869d154dec1180beadbb9df80501"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga43041378b3410ea951b7de0dfd2bc7ee"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, uint, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga43041378b3410ea951b7de0dfd2bc7ee">mediump_umat2</a></td></tr>
<tr class="memdesc:ga43041378b3410ea951b7de0dfd2bc7ee"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier unsigned integer 2x2 matrix.  <a href="a00294.html#ga43041378b3410ea951b7de0dfd2bc7ee">More...</a><br /></td></tr>
<tr class="separator:ga43041378b3410ea951b7de0dfd2bc7ee"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3b209b1b751f041422137e3c065dfa98"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 2, uint, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga3b209b1b751f041422137e3c065dfa98">mediump_umat2x2</a></td></tr>
<tr class="memdesc:ga3b209b1b751f041422137e3c065dfa98"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier unsigned integer 2x2 matrix.  <a href="a00294.html#ga3b209b1b751f041422137e3c065dfa98">More...</a><br /></td></tr>
<tr class="separator:ga3b209b1b751f041422137e3c065dfa98"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaee2c1f13b41f4c92ea5b3efe367a1306"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 3, uint, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gaee2c1f13b41f4c92ea5b3efe367a1306">mediump_umat2x3</a></td></tr>
<tr class="memdesc:gaee2c1f13b41f4c92ea5b3efe367a1306"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier unsigned integer 2x3 matrix.  <a href="a00294.html#gaee2c1f13b41f4c92ea5b3efe367a1306">More...</a><br /></td></tr>
<tr class="separator:gaee2c1f13b41f4c92ea5b3efe367a1306"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae1317ddca16d01e119a40b7f0ee85f95"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 2, 4, uint, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gae1317ddca16d01e119a40b7f0ee85f95">mediump_umat2x4</a></td></tr>
<tr class="memdesc:gae1317ddca16d01e119a40b7f0ee85f95"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier unsigned integer 2x4 matrix.  <a href="a00294.html#gae1317ddca16d01e119a40b7f0ee85f95">More...</a><br /></td></tr>
<tr class="separator:gae1317ddca16d01e119a40b7f0ee85f95"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1730dbe3c67801f53520b06d1aa0a34a"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, uint, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga1730dbe3c67801f53520b06d1aa0a34a">mediump_umat3</a></td></tr>
<tr class="memdesc:ga1730dbe3c67801f53520b06d1aa0a34a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier unsigned integer 3x3 matrix.  <a href="a00294.html#ga1730dbe3c67801f53520b06d1aa0a34a">More...</a><br /></td></tr>
<tr class="separator:ga1730dbe3c67801f53520b06d1aa0a34a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaadc28bfdc8ebca81ae85121b11994970"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 2, uint, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gaadc28bfdc8ebca81ae85121b11994970">mediump_umat3x2</a></td></tr>
<tr class="memdesc:gaadc28bfdc8ebca81ae85121b11994970"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier unsigned integer 3x2 matrix.  <a href="a00294.html#gaadc28bfdc8ebca81ae85121b11994970">More...</a><br /></td></tr>
<tr class="separator:gaadc28bfdc8ebca81ae85121b11994970"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga48f2fc38d3f7fab3cfbc961278ced53d"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 3, uint, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga48f2fc38d3f7fab3cfbc961278ced53d">mediump_umat3x3</a></td></tr>
<tr class="memdesc:ga48f2fc38d3f7fab3cfbc961278ced53d"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier unsigned integer 3x3 matrix.  <a href="a00294.html#ga48f2fc38d3f7fab3cfbc961278ced53d">More...</a><br /></td></tr>
<tr class="separator:ga48f2fc38d3f7fab3cfbc961278ced53d"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga78009a1e4ca64217e46b418535e52546"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 3, 4, uint, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga78009a1e4ca64217e46b418535e52546">mediump_umat3x4</a></td></tr>
<tr class="memdesc:ga78009a1e4ca64217e46b418535e52546"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier unsigned integer 3x4 matrix.  <a href="a00294.html#ga78009a1e4ca64217e46b418535e52546">More...</a><br /></td></tr>
<tr class="separator:ga78009a1e4ca64217e46b418535e52546"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5087c2beb26a11d9af87432e554cf9d1"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, uint, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga5087c2beb26a11d9af87432e554cf9d1">mediump_umat4</a></td></tr>
<tr class="memdesc:ga5087c2beb26a11d9af87432e554cf9d1"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier unsigned integer 4x4 matrix.  <a href="a00294.html#ga5087c2beb26a11d9af87432e554cf9d1">More...</a><br /></td></tr>
<tr class="separator:ga5087c2beb26a11d9af87432e554cf9d1"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf35aefd81cc13718f6b059623f7425fa"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 2, uint, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gaf35aefd81cc13718f6b059623f7425fa">mediump_umat4x2</a></td></tr>
<tr class="memdesc:gaf35aefd81cc13718f6b059623f7425fa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier unsigned integer 4x2 matrix.  <a href="a00294.html#gaf35aefd81cc13718f6b059623f7425fa">More...</a><br /></td></tr>
<tr class="separator:gaf35aefd81cc13718f6b059623f7425fa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4e1bed14fbc7f4b376aaed064f89f0fb"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 3, uint, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga4e1bed14fbc7f4b376aaed064f89f0fb">mediump_umat4x3</a></td></tr>
<tr class="memdesc:ga4e1bed14fbc7f4b376aaed064f89f0fb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier unsigned integer 4x3 matrix.  <a href="a00294.html#ga4e1bed14fbc7f4b376aaed064f89f0fb">More...</a><br /></td></tr>
<tr class="separator:ga4e1bed14fbc7f4b376aaed064f89f0fb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaa9428fc8430dc552aad920653f822ef3"><td class="memItemLeft" align="right" valign="top">typedef mat&lt; 4, 4, uint, mediump &gt;&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gaa9428fc8430dc552aad920653f822ef3">mediump_umat4x4</a></td></tr>
<tr class="memdesc:gaa9428fc8430dc552aad920653f822ef3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Medium-qualifier unsigned integer 4x4 matrix.  <a href="a00294.html#gaa9428fc8430dc552aad920653f822ef3">More...</a><br /></td></tr>
<tr class="separator:gaa9428fc8430dc552aad920653f822ef3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga4cae85566f900debf930c41944b64691"><td class="memItemLeft" align="right" valign="top">typedef mediump_umat2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga4cae85566f900debf930c41944b64691">umat2</a></td></tr>
<tr class="memdesc:ga4cae85566f900debf930c41944b64691"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unsigned integer 2x2 matrix.  <a href="a00294.html#ga4cae85566f900debf930c41944b64691">More...</a><br /></td></tr>
<tr class="separator:ga4cae85566f900debf930c41944b64691"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gabf8acdd33ce8951051edbca5200898aa"><td class="memItemLeft" align="right" valign="top">typedef mediump_umat2x2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gabf8acdd33ce8951051edbca5200898aa">umat2x2</a></td></tr>
<tr class="memdesc:gabf8acdd33ce8951051edbca5200898aa"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unsigned integer 2x2 matrix.  <a href="a00294.html#gabf8acdd33ce8951051edbca5200898aa">More...</a><br /></td></tr>
<tr class="separator:gabf8acdd33ce8951051edbca5200898aa"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1870da7578d5022b973a83155d386ab3"><td class="memItemLeft" align="right" valign="top">typedef mediump_umat2x3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga1870da7578d5022b973a83155d386ab3">umat2x3</a></td></tr>
<tr class="memdesc:ga1870da7578d5022b973a83155d386ab3"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unsigned integer 2x3 matrix.  <a href="a00294.html#ga1870da7578d5022b973a83155d386ab3">More...</a><br /></td></tr>
<tr class="separator:ga1870da7578d5022b973a83155d386ab3"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga57936a3998e992370e59a223e0ee4fd4"><td class="memItemLeft" align="right" valign="top">typedef mediump_umat2x4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga57936a3998e992370e59a223e0ee4fd4">umat2x4</a></td></tr>
<tr class="memdesc:ga57936a3998e992370e59a223e0ee4fd4"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unsigned integer 2x4 matrix.  <a href="a00294.html#ga57936a3998e992370e59a223e0ee4fd4">More...</a><br /></td></tr>
<tr class="separator:ga57936a3998e992370e59a223e0ee4fd4"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga5085e3ff02abbac5e537eb7b89ab63b6"><td class="memItemLeft" align="right" valign="top">typedef mediump_umat3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga5085e3ff02abbac5e537eb7b89ab63b6">umat3</a></td></tr>
<tr class="memdesc:ga5085e3ff02abbac5e537eb7b89ab63b6"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unsigned integer 3x3 matrix.  <a href="a00294.html#ga5085e3ff02abbac5e537eb7b89ab63b6">More...</a><br /></td></tr>
<tr class="separator:ga5085e3ff02abbac5e537eb7b89ab63b6"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga9cd7fa637a4a6788337f45231fad9e1a"><td class="memItemLeft" align="right" valign="top">typedef mediump_umat3x2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga9cd7fa637a4a6788337f45231fad9e1a">umat3x2</a></td></tr>
<tr class="memdesc:ga9cd7fa637a4a6788337f45231fad9e1a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unsigned integer 3x2 matrix.  <a href="a00294.html#ga9cd7fa637a4a6788337f45231fad9e1a">More...</a><br /></td></tr>
<tr class="separator:ga9cd7fa637a4a6788337f45231fad9e1a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga1f2cfcf3357db0cdf31fcb15e3c6bafb"><td class="memItemLeft" align="right" valign="top">typedef mediump_umat3x3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga1f2cfcf3357db0cdf31fcb15e3c6bafb">umat3x3</a></td></tr>
<tr class="memdesc:ga1f2cfcf3357db0cdf31fcb15e3c6bafb"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unsigned integer 3x3 matrix.  <a href="a00294.html#ga1f2cfcf3357db0cdf31fcb15e3c6bafb">More...</a><br /></td></tr>
<tr class="separator:ga1f2cfcf3357db0cdf31fcb15e3c6bafb"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gae7c78ff3fc4309605ab0fa186c8d48ba"><td class="memItemLeft" align="right" valign="top">typedef mediump_umat3x4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gae7c78ff3fc4309605ab0fa186c8d48ba">umat3x4</a></td></tr>
<tr class="memdesc:gae7c78ff3fc4309605ab0fa186c8d48ba"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unsigned integer 3x4 matrix.  <a href="a00294.html#gae7c78ff3fc4309605ab0fa186c8d48ba">More...</a><br /></td></tr>
<tr class="separator:gae7c78ff3fc4309605ab0fa186c8d48ba"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga38bc7bb6494e344185df596deeb4544c"><td class="memItemLeft" align="right" valign="top">typedef mediump_umat4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga38bc7bb6494e344185df596deeb4544c">umat4</a></td></tr>
<tr class="memdesc:ga38bc7bb6494e344185df596deeb4544c"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unsigned integer 4x4 matrix.  <a href="a00294.html#ga38bc7bb6494e344185df596deeb4544c">More...</a><br /></td></tr>
<tr class="separator:ga38bc7bb6494e344185df596deeb4544c"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga70fa2d05896aa83cbc8c07672a429b53"><td class="memItemLeft" align="right" valign="top">typedef mediump_umat4x2&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga70fa2d05896aa83cbc8c07672a429b53">umat4x2</a></td></tr>
<tr class="memdesc:ga70fa2d05896aa83cbc8c07672a429b53"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unsigned integer 4x2 matrix.  <a href="a00294.html#ga70fa2d05896aa83cbc8c07672a429b53">More...</a><br /></td></tr>
<tr class="separator:ga70fa2d05896aa83cbc8c07672a429b53"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga87581417945411f75cb31dd6ca1dba98"><td class="memItemLeft" align="right" valign="top">typedef mediump_umat4x3&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#ga87581417945411f75cb31dd6ca1dba98">umat4x3</a></td></tr>
<tr class="memdesc:ga87581417945411f75cb31dd6ca1dba98"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unsigned integer 4x3 matrix.  <a href="a00294.html#ga87581417945411f75cb31dd6ca1dba98">More...</a><br /></td></tr>
<tr class="separator:ga87581417945411f75cb31dd6ca1dba98"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaf72e6d399c42985db6872c50f53d7eb8"><td class="memItemLeft" align="right" valign="top">typedef mediump_umat4x4&#160;</td><td class="memItemRight" valign="bottom"><a class="el" href="a00294.html#gaf72e6d399c42985db6872c50f53d7eb8">umat4x4</a></td></tr>
<tr class="memdesc:gaf72e6d399c42985db6872c50f53d7eb8"><td class="mdescLeft">&#160;</td><td class="mdescRight">Unsigned integer 4x4 matrix.  <a href="a00294.html#gaf72e6d399c42985db6872c50f53d7eb8">More...</a><br /></td></tr>
<tr class="separator:gaf72e6d399c42985db6872c50f53d7eb8"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00100.html" title="GLM_GTC_matrix_integer ">glm/gtc/matrix_integer.hpp</a>&gt; to use the features of this extension. </p>
<p>Defines a number of matrices with integer types. </p>
<h2 class="groupheader">Typedef Documentation</h2>
<a class="anchor" id="ga8499cc3b016003f835314c1c756e9db9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 2, int, highp&gt; highp_imat2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier signed integer 2x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00037">37</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaa389e2d1c3b10941cae870bc0aeba5b3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 2, int, highp&gt; highp_imat2x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier signed integer 2x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00049">49</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaba49d890e06c9444795f5a133fbf1336"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 3, int, highp&gt; highp_imat2x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier signed integer 2x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00053">53</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga05a970fd4366dad6c8a0be676b1eae5b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 4, int, highp&gt; highp_imat2x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier signed integer 2x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00057">57</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaca4506a3efa679eff7c006d9826291fd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 3, int, highp&gt; highp_imat3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier signed integer 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00041">41</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga91c671c3ff9706c2393e78b22fd84bcb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 2, int, highp&gt; highp_imat3x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier signed integer 3x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00061">61</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga07d7b7173e2a6f843ff5f1c615a95b41"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 3, int, highp&gt; highp_imat3x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier signed integer 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00065">65</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga53008f580be99018a17b357b5a4ffc0d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 4, int, highp&gt; highp_imat3x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier signed integer 3x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00069">69</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga7cfb09b34e0fcf73eaf6512d6483ef56"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 4, int, highp&gt; highp_imat4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier signed integer 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00045">45</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga1858820fb292cae396408b2034407f72"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 2, int, highp&gt; highp_imat4x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier signed integer 4x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00073">73</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga6be0b80ae74bb309bc5b964d93d68fc5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 3, int, highp&gt; highp_imat4x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier signed integer 4x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00077">77</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga2c783ee6f8f040ab37df2f70392c8b44"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 4, int, highp&gt; highp_imat4x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier signed integer 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00081">81</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga42cbce64c4c1cd121b8437daa6e110de"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 2, uint, highp&gt; highp_umat2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier unsigned integer 2x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00186">186</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga5337b7bc95f9cbac08a0c00b3f936b28"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 2, uint, highp&gt; highp_umat2x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier unsigned integer 2x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00198">198</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga90718c7128320b24b52f9ea70e643ad4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 3, uint, highp&gt; highp_umat2x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier unsigned integer 2x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00202">202</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gadca0a4724b4a6f56a2355b6f6e19248b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 4, uint, highp&gt; highp_umat2x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier unsigned integer 2x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00206">206</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaa1143120339b7d2d469d327662e8a172"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 3, uint, highp&gt; highp_umat3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier unsigned integer 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00190">190</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga844a5da2e7fc03fc7cccc7f1b70809c4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 2, uint, highp&gt; highp_umat3x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier unsigned integer 3x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00210">210</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga1f7d41c36b980774a4d2e7c1647fb4b2"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 3, uint, highp&gt; highp_umat3x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier unsigned integer 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00214">214</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga25ee15c323924f2d0fe9896d329e5086"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 4, uint, highp&gt; highp_umat3x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier unsigned integer 3x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00218">218</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaf665e4e78c2cc32a54ab40325738f9c9"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 4, uint, highp&gt; highp_umat4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier unsigned integer 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00194">194</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gae69eb82ec08b0dc9bf2ead2a339ff801"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 2, uint, highp&gt; highp_umat4x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier unsigned integer 4x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00222">222</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga45a8163d02c43216252056b0c120f3a5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 3, uint, highp&gt; highp_umat4x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier unsigned integer 4x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00226">226</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga6a56cbb769aed334c95241664415f9ba"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 4, uint, highp&gt; highp_umat4x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>High-qualifier unsigned integer 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00230">230</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaabe04f9948d4a213bb1c20137de03e01"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_imat2 imat2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Signed integer 2x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00362">362</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaa4732a240522ad9bc28144fda2fc14ec"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_imat2x2 imat2x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Signed integer 2x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00374">374</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga3f42dd3d5d94a0fd5706f7ec8dd0c605"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_imat2x3 imat2x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Signed integer 2x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00378">378</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga9d8faafdca42583d67e792dd038fc668"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_imat2x4 imat2x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Signed integer 2x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00382">382</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga038f68437155ffa3c2583a15264a8195"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_imat3 imat3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Signed integer 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00366">366</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga7b33bbe4f12c060892bd3cc8d4cd737f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_imat3x2 imat3x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Signed integer 3x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00386">386</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga6aacc960f62e8f7d2fe9d32d5050e7a4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_imat3x3 imat3x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Signed integer 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00390">390</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga6e9ce23496d8b08dfc302d4039694b58"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_imat3x4 imat3x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Signed integer 3x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00394">394</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga96b0d26a33b81bb6a60ca0f39682f7eb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_imat4 imat4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Signed integer 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00370">370</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga8ce7ef51d8b2c1901fa5414deccbc3fa"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_imat4x2 imat4x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Signed integer 4x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00398">398</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga705ee0bf49d6c3de4404ce2481bf0df5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_imat4x3 imat4x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Signed integer 4x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00402">402</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga43ed5e4f475b6f4cad7cba78f29c405b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_imat4x4 imat4x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Signed integer 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00406">406</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaa0bff0be804142bb16d441aec0a7962e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 2, int, lowp&gt; lowp_imat2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier signed integer 2x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00136">136</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga92b95b679975d408645547ab45a8dcd8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 2, int, lowp&gt; lowp_imat2x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier signed integer 2x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00149">149</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga8c9e7a388f8e7c52f1e6857dee8afb65"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 3, int, lowp&gt; lowp_imat2x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier signed integer 2x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00153">153</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga9cc13bd1f8dd2933e9fa31fe3f70e16e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 4, int, lowp&gt; lowp_imat2x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier signed integer 2x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00157">157</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga69bfe668f4170379fc1f35d82b060c43"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 3, int, lowp&gt; lowp_imat3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier signed integer 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00140">140</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga33db8f27491d30906cd37c0d86b3f432"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 2, int, lowp&gt; lowp_imat3x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier signed integer 3x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00161">161</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga664f061df00020048c3f8530329ace45"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 3, int, lowp&gt; lowp_imat3x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier signed integer 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00165">165</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga9273faab33623d944af4080befbb2c80"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 4, int, lowp&gt; lowp_imat3x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier signed integer 3x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00169">169</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gad1e77f7270cad461ca4fcb4c3ec2e98c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 4, int, lowp&gt; lowp_imat4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier signed integer 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00144">144</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga26ec1a2ba08a1488f5f05336858a0f09"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 2, int, lowp&gt; lowp_imat4x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier signed integer 4x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00173">173</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga8f40483a3ae634ead8ad22272c543a33"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 3, int, lowp&gt; lowp_imat4x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier signed integer 4x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00177">177</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaf65677e53ac8e31a107399340d5e2451"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 4, int, lowp&gt; lowp_imat4x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier signed integer 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00181">181</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaf2fba702d990437fc88ff3f3a76846ee"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 2, uint, lowp&gt; lowp_umat2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier unsigned integer 2x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00285">285</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga7b2e9d89745f7175051284e54c81d81c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 2, uint, lowp&gt; lowp_umat2x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier unsigned integer 2x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00298">298</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga3072f90fd86f17a862e21589fbb14c0f"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 3, uint, lowp&gt; lowp_umat2x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier unsigned integer 2x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00302">302</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga8bb45fec4bd77bd81b4ae7eb961a270d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 4, uint, lowp&gt; lowp_umat2x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier unsigned integer 2x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00306">306</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaf1145f72bcdd590f5808c4bc170c2924"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 3, uint, lowp&gt; lowp_umat3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier unsigned integer 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00289">289</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga56ea68c6a6cba8d8c21d17bb14e69c6b"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 2, uint, lowp&gt; lowp_umat3x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier unsigned integer 3x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00310">310</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga4f660a39a395cc14f018f985e7dfbeb5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 3, uint, lowp&gt; lowp_umat3x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier unsigned integer 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00314">314</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaec3d624306bd59649f021864709d56b5"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 4, uint, lowp&gt; lowp_umat3x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier unsigned integer 3x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00318">318</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gac092c6105827bf9ea080db38074b78eb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 4, uint, lowp&gt; lowp_umat4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier unsigned integer 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00293">293</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga7716c2b210d141846f1ac4e774adef5e"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 2, uint, lowp&gt; lowp_umat4x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier unsigned integer 4x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00322">322</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga09ab33a2636f5f43f7fae29cfbc20fff"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 3, uint, lowp&gt; lowp_umat4x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier unsigned integer 4x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00326">326</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga10aafc66cf1a0ece336b1c5ae13d0cc0"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 4, uint, lowp&gt; lowp_umat4x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Low-qualifier unsigned integer 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00330">330</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga20f4cc7ab23e2aa1f4db9fdb5496d378"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 2, int, mediump&gt; mediump_imat2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier signed integer 2x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00086">86</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga4b2aeb11a329940721dda9583e71f856"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 2, int, mediump&gt; mediump_imat2x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier signed integer 2x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00099">99</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga74362470ba99843ac70aee5ac38cc674"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 3, int, mediump&gt; mediump_imat2x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier signed integer 2x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00103">103</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga8da25cd380ba30fc5b68a4687deb3e09"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 4, int, mediump&gt; mediump_imat2x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier signed integer 2x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00107">107</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga6c63bdc736efd3466e0730de0251cb71"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 3, int, mediump&gt; mediump_imat3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier signed integer 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00090">90</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gac0b4e42d648fb3eaf4bb88da82ecc809"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 2, int, mediump&gt; mediump_imat3x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier signed integer 3x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00111">111</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gad99cc2aad8fc57f068cfa7719dbbea12"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 3, int, mediump&gt; mediump_imat3x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier signed integer 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00115">115</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga67689a518b181a26540bc44a163525cd"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 4, int, mediump&gt; mediump_imat3x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier signed integer 3x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00119">119</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaf348552978553630d2a00b78eb887ced"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 4, int, mediump&gt; mediump_imat4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier signed integer 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00094">94</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga8b2d35816f7103f0f4c82dd2f27571fc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 2, int, mediump&gt; mediump_imat4x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier signed integer 4x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00123">123</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga5b10acc696759e03f6ab918f4467e94c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 3, int, mediump&gt; mediump_imat4x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier signed integer 4x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00127">127</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga2596869d154dec1180beadbb9df80501"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 4, int, mediump&gt; mediump_imat4x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier signed integer 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00131">131</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga43041378b3410ea951b7de0dfd2bc7ee"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 2, uint, mediump&gt; mediump_umat2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier unsigned integer 2x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00235">235</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga3b209b1b751f041422137e3c065dfa98"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 2, uint, mediump&gt; mediump_umat2x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier unsigned integer 2x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00248">248</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaee2c1f13b41f4c92ea5b3efe367a1306"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 3, uint, mediump&gt; mediump_umat2x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier unsigned integer 2x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00252">252</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gae1317ddca16d01e119a40b7f0ee85f95"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;2, 4, uint, mediump&gt; mediump_umat2x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier unsigned integer 2x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00256">256</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga1730dbe3c67801f53520b06d1aa0a34a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 3, uint, mediump&gt; mediump_umat3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier unsigned integer 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00239">239</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaadc28bfdc8ebca81ae85121b11994970"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 2, uint, mediump&gt; mediump_umat3x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier unsigned integer 3x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00260">260</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga48f2fc38d3f7fab3cfbc961278ced53d"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 3, uint, mediump&gt; mediump_umat3x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier unsigned integer 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00264">264</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga78009a1e4ca64217e46b418535e52546"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;3, 4, uint, mediump&gt; mediump_umat3x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier unsigned integer 3x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00268">268</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga5087c2beb26a11d9af87432e554cf9d1"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 4, uint, mediump&gt; mediump_umat4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier unsigned integer 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00243">243</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaf35aefd81cc13718f6b059623f7425fa"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 2, uint, mediump&gt; mediump_umat4x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier unsigned integer 4x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00272">272</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga4e1bed14fbc7f4b376aaed064f89f0fb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 3, uint, mediump&gt; mediump_umat4x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier unsigned integer 4x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00276">276</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaa9428fc8430dc552aad920653f822ef3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mat&lt;4, 4, uint, mediump&gt; mediump_umat4x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Medium-qualifier unsigned integer 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00280">280</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga4cae85566f900debf930c41944b64691"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_umat2 umat2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Unsigned integer 2x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00439">439</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gabf8acdd33ce8951051edbca5200898aa"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_umat2x2 umat2x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Unsigned integer 2x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00451">451</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga1870da7578d5022b973a83155d386ab3"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_umat2x3 umat2x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Unsigned integer 2x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00455">455</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga57936a3998e992370e59a223e0ee4fd4"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_umat2x4 umat2x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Unsigned integer 2x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00459">459</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga5085e3ff02abbac5e537eb7b89ab63b6"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_umat3 umat3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Unsigned integer 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00443">443</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga9cd7fa637a4a6788337f45231fad9e1a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_umat3x2 umat3x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Unsigned integer 3x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00463">463</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga1f2cfcf3357db0cdf31fcb15e3c6bafb"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_umat3x3 umat3x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Unsigned integer 3x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00467">467</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gae7c78ff3fc4309605ab0fa186c8d48ba"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_umat3x4 umat3x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Unsigned integer 3x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00471">471</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga38bc7bb6494e344185df596deeb4544c"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_umat4 umat4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Unsigned integer 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00447">447</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga70fa2d05896aa83cbc8c07672a429b53"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_umat4x2 umat4x2</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Unsigned integer 4x2 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00475">475</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="ga87581417945411f75cb31dd6ca1dba98"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_umat4x3 umat4x3</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Unsigned integer 4x3 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00479">479</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
<a class="anchor" id="gaf72e6d399c42985db6872c50f53d7eb8"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">typedef mediump_umat4x4 umat4x4</td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Unsigned integer 4x4 matrix. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00294.html" title="Include <glm/gtc/matrix_integer.hpp> to use the features of this extension. ">GLM_GTC_matrix_integer</a> </dd></dl>

<p>Definition at line <a class="el" href="a00100_source.html#l00483">483</a> of file <a class="el" href="a00100_source.html">matrix_integer.hpp</a>.</p>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
