<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/xhtml;charset=UTF-8"/>
<meta http-equiv="X-UA-Compatible" content="IE=9"/>
<meta name="generator" content="Doxygen 1.8.10"/>
<title>0.9.9 API documentation: GLM_EXT_scalar_integer</title>
<link href="tabs.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="jquery.js"></script>
<script type="text/javascript" src="dynsections.js"></script>
<link href="search/search.css" rel="stylesheet" type="text/css"/>
<script type="text/javascript" src="search/searchdata.js"></script>
<script type="text/javascript" src="search/search.js"></script>
<script type="text/javascript">
  $(document).ready(function() { init_search(); });
</script>
<link href="doxygen.css" rel="stylesheet" type="text/css" />
</head>
<body>
<div id="top"><!-- do not remove this div, it is closed by doxygen! -->
<div id="titlearea">
<table cellspacing="0" cellpadding="0">
 <tbody>
 <tr style="height: 56px;">
  <td id="projectlogo"><img alt="Logo" src="logo-mini.png"/></td>
  <td id="projectalign" style="padding-left: 0.5em;">
   <div id="projectname">0.9.9 API documentation
   </div>
  </td>
 </tr>
 </tbody>
</table>
</div>
<!-- end header part -->
<!-- Generated by Doxygen 1.8.10 -->
<script type="text/javascript">
var searchBox = new SearchBox("searchBox", "search",false,'Search');
</script>
  <div id="navrow1" class="tabs">
    <ul class="tablist">
      <li><a href="index.html"><span>Main&#160;Page</span></a></li>
      <li><a href="modules.html"><span>Modules</span></a></li>
      <li><a href="files.html"><span>Files</span></a></li>
      <li>
        <div id="MSearchBox" class="MSearchBoxInactive">
        <span class="left">
          <img id="MSearchSelect" src="search/mag_sel.png"
               onmouseover="return searchBox.OnSearchSelectShow()"
               onmouseout="return searchBox.OnSearchSelectHide()"
               alt=""/>
          <input type="text" id="MSearchField" value="Search" accesskey="S"
               onfocus="searchBox.OnSearchFieldFocus(true)" 
               onblur="searchBox.OnSearchFieldFocus(false)" 
               onkeyup="searchBox.OnSearchFieldChange(event)"/>
          </span><span class="right">
            <a id="MSearchClose" href="javascript:searchBox.CloseResultsWindow()"><img id="MSearchCloseImg" border="0" src="search/close.png" alt=""/></a>
          </span>
        </div>
      </li>
    </ul>
  </div>
</div><!-- top -->
<!-- window showing the filter options -->
<div id="MSearchSelectWindow"
     onmouseover="return searchBox.OnSearchSelectShow()"
     onmouseout="return searchBox.OnSearchSelectHide()"
     onkeydown="return searchBox.OnSearchSelectKey(event)">
</div>

<!-- iframe showing the search results (closed by default) -->
<div id="MSearchResultsWindow">
<iframe src="javascript:void(0)" frameborder="0" 
        name="MSearchResults" id="MSearchResults">
</iframe>
</div>

<div class="header">
  <div class="summary">
<a href="#func-members">Functions</a>  </div>
  <div class="headertitle">
<div class="title">GLM_EXT_scalar_integer<div class="ingroups"><a class="el" href="a00285.html">Stable extensions</a></div></div>  </div>
</div><!--header-->
<div class="contents">

<p>Include &lt;<a class="el" href="a00147.html" title="GLM_EXT_scalar_integer ">glm/ext/scalar_integer.hpp</a>&gt; to use the features of this extension.  
<a href="#details">More...</a></p>
<table class="memberdecls">
<tr class="heading"><td colspan="2"><h2 class="groupheader"><a name="func-members"></a>
Functions</h2></td></tr>
<tr class="memitem:ga2777901e41ad6e1e9d0ad6cc855d1075"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:ga2777901e41ad6e1e9d0ad6cc855d1075"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL int&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00261.html#ga2777901e41ad6e1e9d0ad6cc855d1075">findNSB</a> (genIUType x, int significantBitCount)</td></tr>
<tr class="memdesc:ga2777901e41ad6e1e9d0ad6cc855d1075"><td class="mdescLeft">&#160;</td><td class="mdescRight">Returns the bit number of the Nth significant bit set to 1 in the binary representation of value.  <a href="a00261.html#ga2777901e41ad6e1e9d0ad6cc855d1075">More...</a><br /></td></tr>
<tr class="separator:ga2777901e41ad6e1e9d0ad6cc855d1075"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gaec593d33956a8fe43f78fccc63ddde9a"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:gaec593d33956a8fe43f78fccc63ddde9a"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00261.html#gaec593d33956a8fe43f78fccc63ddde9a">isMultiple</a> (genIUType v, genIUType Multiple)</td></tr>
<tr class="memdesc:gaec593d33956a8fe43f78fccc63ddde9a"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return true if the 'Value' is a multiple of 'Multiple'.  <a href="a00261.html#gaec593d33956a8fe43f78fccc63ddde9a">More...</a><br /></td></tr>
<tr class="separator:gaec593d33956a8fe43f78fccc63ddde9a"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gadf491730354aa7da67fbe23d4d688763"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:gadf491730354aa7da67fbe23d4d688763"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL bool&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00261.html#gadf491730354aa7da67fbe23d4d688763">isPowerOfTwo</a> (genIUType v)</td></tr>
<tr class="memdesc:gadf491730354aa7da67fbe23d4d688763"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return true if the value is a power of two number.  <a href="a00261.html#gadf491730354aa7da67fbe23d4d688763">More...</a><br /></td></tr>
<tr class="separator:gadf491730354aa7da67fbe23d4d688763"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab770a3835c44c8a6fd225be4f4e6b317"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:gab770a3835c44c8a6fd225be4f4e6b317"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00261.html#gab770a3835c44c8a6fd225be4f4e6b317">nextMultiple</a> (genIUType v, genIUType Multiple)</td></tr>
<tr class="memdesc:gab770a3835c44c8a6fd225be4f4e6b317"><td class="mdescLeft">&#160;</td><td class="mdescRight">Higher multiple number of Source.  <a href="a00261.html#gab770a3835c44c8a6fd225be4f4e6b317">More...</a><br /></td></tr>
<tr class="separator:gab770a3835c44c8a6fd225be4f4e6b317"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:ga3a37c2f2fd347886c9af6a3ca3db04dc"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:ga3a37c2f2fd347886c9af6a3ca3db04dc"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00261.html#ga3a37c2f2fd347886c9af6a3ca3db04dc">nextPowerOfTwo</a> (genIUType v)</td></tr>
<tr class="memdesc:ga3a37c2f2fd347886c9af6a3ca3db04dc"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the power of two number which value is just higher the input value, round up to a power of two.  <a href="a00261.html#ga3a37c2f2fd347886c9af6a3ca3db04dc">More...</a><br /></td></tr>
<tr class="separator:ga3a37c2f2fd347886c9af6a3ca3db04dc"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gada3bdd871ffe31f2d484aa668362f636"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:gada3bdd871ffe31f2d484aa668362f636"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00261.html#gada3bdd871ffe31f2d484aa668362f636">prevMultiple</a> (genIUType v, genIUType Multiple)</td></tr>
<tr class="memdesc:gada3bdd871ffe31f2d484aa668362f636"><td class="mdescLeft">&#160;</td><td class="mdescRight">Lower multiple number of Source.  <a href="a00261.html#gada3bdd871ffe31f2d484aa668362f636">More...</a><br /></td></tr>
<tr class="separator:gada3bdd871ffe31f2d484aa668362f636"><td class="memSeparator" colspan="2">&#160;</td></tr>
<tr class="memitem:gab21902a0e7e5a8451a7ad80333618727"><td class="memTemplParams" colspan="2">template&lt;typename genIUType &gt; </td></tr>
<tr class="memitem:gab21902a0e7e5a8451a7ad80333618727"><td class="memTemplItemLeft" align="right" valign="top">GLM_FUNC_DECL genIUType&#160;</td><td class="memTemplItemRight" valign="bottom"><a class="el" href="a00261.html#gab21902a0e7e5a8451a7ad80333618727">prevPowerOfTwo</a> (genIUType v)</td></tr>
<tr class="memdesc:gab21902a0e7e5a8451a7ad80333618727"><td class="mdescLeft">&#160;</td><td class="mdescRight">Return the power of two number which value is just lower the input value, round down to a power of two.  <a href="a00261.html#gab21902a0e7e5a8451a7ad80333618727">More...</a><br /></td></tr>
<tr class="separator:gab21902a0e7e5a8451a7ad80333618727"><td class="memSeparator" colspan="2">&#160;</td></tr>
</table>
<a name="details" id="details"></a><h2 class="groupheader">Detailed Description</h2>
<p>Include &lt;<a class="el" href="a00147.html" title="GLM_EXT_scalar_integer ">glm/ext/scalar_integer.hpp</a>&gt; to use the features of this extension. </p>
<h2 class="groupheader">Function Documentation</h2>
<a class="anchor" id="ga2777901e41ad6e1e9d0ad6cc855d1075"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL int glm::findNSB </td>
          <td>(</td>
          <td class="paramtype">genIUType&#160;</td>
          <td class="paramname"><em>x</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">int&#160;</td>
          <td class="paramname"><em>significantBitCount</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Returns the bit number of the Nth significant bit set to 1 in the binary representation of value. </p>
<p>If value bitcount is less than the Nth significant bit, -1 will be returned.</p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genIUType</td><td>Signed or unsigned integer scalar types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00261.html" title="Include <glm/ext/scalar_integer.hpp> to use the features of this extension. ">GLM_EXT_scalar_integer</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gaec593d33956a8fe43f78fccc63ddde9a"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL bool glm::isMultiple </td>
          <td>(</td>
          <td class="paramtype">genIUType&#160;</td>
          <td class="paramname"><em>v</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genIUType&#160;</td>
          <td class="paramname"><em>Multiple</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return true if the 'Value' is a multiple of 'Multiple'. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00261.html" title="Include <glm/ext/scalar_integer.hpp> to use the features of this extension. ">GLM_EXT_scalar_integer</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gadf491730354aa7da67fbe23d4d688763"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL bool glm::isPowerOfTwo </td>
          <td>(</td>
          <td class="paramtype">genIUType&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return true if the value is a power of two number. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00261.html" title="Include <glm/ext/scalar_integer.hpp> to use the features of this extension. ">GLM_EXT_scalar_integer</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab770a3835c44c8a6fd225be4f4e6b317"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genIUType glm::nextMultiple </td>
          <td>(</td>
          <td class="paramtype">genIUType&#160;</td>
          <td class="paramname"><em>v</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genIUType&#160;</td>
          <td class="paramname"><em>Multiple</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Higher multiple number of Source. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genIUType</td><td>Integer scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">v</td><td>Source value to which is applied the function </td></tr>
    <tr><td class="paramname">Multiple</td><td>Must be a null or positive value</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00261.html" title="Include <glm/ext/scalar_integer.hpp> to use the features of this extension. ">GLM_EXT_scalar_integer</a> </dd></dl>

</div>
</div>
<a class="anchor" id="ga3a37c2f2fd347886c9af6a3ca3db04dc"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genIUType glm::nextPowerOfTwo </td>
          <td>(</td>
          <td class="paramtype">genIUType&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the power of two number which value is just higher the input value, round up to a power of two. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00261.html" title="Include <glm/ext/scalar_integer.hpp> to use the features of this extension. ">GLM_EXT_scalar_integer</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gada3bdd871ffe31f2d484aa668362f636"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genIUType glm::prevMultiple </td>
          <td>(</td>
          <td class="paramtype">genIUType&#160;</td>
          <td class="paramname"><em>v</em>, </td>
        </tr>
        <tr>
          <td class="paramkey"></td>
          <td></td>
          <td class="paramtype">genIUType&#160;</td>
          <td class="paramname"><em>Multiple</em>&#160;</td>
        </tr>
        <tr>
          <td></td>
          <td>)</td>
          <td></td><td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Lower multiple number of Source. </p>
<dl class="tparams"><dt>Template Parameters</dt><dd>
  <table class="tparams">
    <tr><td class="paramname">genIUType</td><td>Integer scalar or vector types.</td></tr>
  </table>
  </dd>
</dl>
<dl class="params"><dt>Parameters</dt><dd>
  <table class="params">
    <tr><td class="paramname">v</td><td>Source value to which is applied the function </td></tr>
    <tr><td class="paramname">Multiple</td><td>Must be a null or positive value</td></tr>
  </table>
  </dd>
</dl>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00261.html" title="Include <glm/ext/scalar_integer.hpp> to use the features of this extension. ">GLM_EXT_scalar_integer</a> </dd></dl>

</div>
</div>
<a class="anchor" id="gab21902a0e7e5a8451a7ad80333618727"></a>
<div class="memitem">
<div class="memproto">
      <table class="memname">
        <tr>
          <td class="memname">GLM_FUNC_DECL genIUType glm::prevPowerOfTwo </td>
          <td>(</td>
          <td class="paramtype">genIUType&#160;</td>
          <td class="paramname"><em>v</em></td><td>)</td>
          <td></td>
        </tr>
      </table>
</div><div class="memdoc">

<p>Return the power of two number which value is just lower the input value, round down to a power of two. </p>
<dl class="section see"><dt>See also</dt><dd><a class="el" href="a00261.html" title="Include <glm/ext/scalar_integer.hpp> to use the features of this extension. ">GLM_EXT_scalar_integer</a> </dd></dl>

</div>
</div>
</div><!-- contents -->
<!-- start footer part -->
<hr class="footer"/><address class="footer"><small>
Generated by &#160;<a href="http://www.doxygen.org/index.html">
<img class="footer" src="doxygen.png" alt="doxygen"/>
</a> 1.8.10
</small></address>
</body>
</html>
